<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标状态检查</title>
    
    <!-- 本地Font Awesome -->
    <link rel="stylesheet" href="fontawesome/all.min.css">
    <!-- 备用图标样式 -->
    <link rel="stylesheet" href="icons-fallback.css">
    
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #3b82f6;
        }
        
        .status-success {
            border-left-color: #10b981;
            background: #ecfdf5;
        }
        
        .status-warning {
            border-left-color: #f59e0b;
            background: #fffbeb;
        }
        
        .status-error {
            border-left-color: #ef4444;
            background: #fef2f2;
        }
        
        .icon-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .icon-item {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .icon-item:hover {
            transform: translateY(-2px);
        }
        
        .icon-item i {
            font-size: 24px;
            margin-right: 10px;
            color: #3b82f6;
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: background 0.2s;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
        
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #f1f5f9;
            border: 1px solid #cbd5e1;
        }
        
        .loading {
            display: inline-block;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-check-circle"></i> 图标状态检查</h1>
        
        <div class="status-card" id="mainStatus">
            <h3><i class="fas fa-spinner loading"></i> 正在检查图标状态...</h3>
            <p>请稍候，系统正在检测Font Awesome图标的加载状态。</p>
        </div>
        
        <div class="status-card">
            <h3><i class="fas fa-info-circle"></i> 图标演示</h3>
            <p>以下是一些常用图标的显示测试：</p>
            
            <div class="icon-demo">
                <div class="icon-item">
                    <i class="fas fa-industry"></i>
                    <span>工厂</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-chart-line"></i>
                    <span>图表</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-search"></i>
                    <span>搜索</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-cogs"></i>
                    <span>设置</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-users"></i>
                    <span>用户</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-microchip"></i>
                    <span>设备</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-tools"></i>
                    <span>工具</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-award"></i>
                    <span>质量</span>
                </div>
            </div>
        </div>
        
        <div class="status-card">
            <h3><i class="fas fa-tools"></i> 测试工具</h3>
            <p>使用以下按钮进行各种测试：</p>
            
            <button class="test-button" onclick="testFontLoading()">
                <i class="fas fa-font"></i> 测试字体加载
            </button>
            
            <button class="test-button" onclick="testCSSRules()">
                <i class="fas fa-code"></i> 测试CSS规则
            </button>
            
            <button class="test-button" onclick="testNetworkConnection()">
                <i class="fas fa-wifi"></i> 测试网络连接
            </button>
            
            <button class="test-button" onclick="toggleFallback()">
                <i class="fas fa-exchange-alt"></i> 切换备用方案
            </button>
            
            <div class="result" id="testResult" style="display: none;">
                <h4>测试结果：</h4>
                <div id="testOutput"></div>
            </div>
        </div>
        
        <div class="status-card">
            <h3><i class="fas fa-question-circle"></i> 故障排除</h3>
            <p>如果图标显示异常，请尝试以下解决方案：</p>
            <ul>
                <li>刷新页面重新加载资源</li>
                <li>检查网络连接是否正常</li>
                <li>清除浏览器缓存</li>
                <li>禁用广告拦截器</li>
                <li>使用备用图标方案</li>
            </ul>
        </div>
    </div>

    <script>
        // 检查Font Awesome加载状态
        function checkFontAwesome() {
            const mainStatus = document.getElementById('mainStatus');
            
            try {
                // 创建测试元素
                const testElement = document.createElement('i');
                testElement.className = 'fas fa-home';
                testElement.style.position = 'absolute';
                testElement.style.left = '-9999px';
                document.body.appendChild(testElement);
                
                // 检查字体族
                const computedStyle = window.getComputedStyle(testElement, ':before');
                const fontFamily = computedStyle.getPropertyValue('font-family');
                const content = computedStyle.getPropertyValue('content');
                
                document.body.removeChild(testElement);
                
                // 判断加载状态
                const isLoaded = fontFamily.includes('Font Awesome') || 
                                (content && content !== 'none' && content !== '""');
                
                if (isLoaded) {
                    mainStatus.className = 'status-card status-success';
                    mainStatus.innerHTML = `
                        <h3><i class="fas fa-check-circle"></i> 图标加载成功！</h3>
                        <p>Font Awesome图标字体已正确加载，所有图标应该正常显示。</p>
                        <p><strong>字体族：</strong> ${fontFamily}</p>
                    `;
                } else {
                    mainStatus.className = 'status-card status-warning';
                    mainStatus.innerHTML = `
                        <h3><i class="fas fa-exclamation-triangle"></i> 图标加载异常</h3>
                        <p>Font Awesome图标字体可能未正确加载，正在使用备用显示方案。</p>
                        <p><strong>检测到的字体族：</strong> ${fontFamily}</p>
                    `;
                    
                    // 启用备用方案
                    document.body.classList.add('icon-fallback');
                }
                
            } catch (error) {
                mainStatus.className = 'status-card status-error';
                mainStatus.innerHTML = `
                    <h3><i class="fas fa-times-circle"></i> 检测失败</h3>
                    <p>无法检测图标加载状态：${error.message}</p>
                `;
            }
        }
        
        // 测试字体加载
        function testFontLoading() {
            showTestResult('正在测试字体加载...');
            
            if ('fonts' in document) {
                document.fonts.load('900 1em "Font Awesome 6 Free"').then(() => {
                    showTestResult('✅ Font Awesome 字体加载成功');
                }).catch(() => {
                    showTestResult('❌ Font Awesome 字体加载失败');
                });
            } else {
                showTestResult('⚠️ 浏览器不支持Font Loading API');
            }
        }
        
        // 测试CSS规则
        function testCSSRules() {
            showTestResult('正在检查CSS规则...');
            
            let foundRules = 0;
            const stylesheets = Array.from(document.styleSheets);
            
            for (let stylesheet of stylesheets) {
                try {
                    if (stylesheet.href && stylesheet.href.includes('font-awesome')) {
                        const rules = Array.from(stylesheet.cssRules || stylesheet.rules || []);
                        foundRules += rules.filter(rule => 
                            rule.selectorText && rule.selectorText.includes('.fa')
                        ).length;
                    }
                } catch (e) {
                    // 跨域样式表无法访问
                }
            }
            
            showTestResult(`找到 ${foundRules} 个Font Awesome CSS规则`);
        }
        
        // 测试网络连接
        function testNetworkConnection() {
            showTestResult('正在测试网络连接...');
            
            const testUrls = [
                'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css',
                'https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.5.1/css/all.min.css'
            ];
            
            let results = [];
            let completed = 0;
            
            testUrls.forEach((url, index) => {
                fetch(url, { method: 'HEAD' })
                    .then(response => {
                        results[index] = `✅ CDN ${index + 1}: ${response.status}`;
                    })
                    .catch(error => {
                        results[index] = `❌ CDN ${index + 1}: 失败`;
                    })
                    .finally(() => {
                        completed++;
                        if (completed === testUrls.length) {
                            showTestResult(results.join('<br>'));
                        }
                    });
            });
        }
        
        // 切换备用方案
        function toggleFallback() {
            const body = document.body;
            const isEnabled = body.classList.contains('icon-fallback');
            
            if (isEnabled) {
                body.classList.remove('icon-fallback');
                showTestResult('✅ 已禁用备用图标方案');
            } else {
                body.classList.add('icon-fallback');
                showTestResult('🎨 已启用备用图标方案');
            }
        }
        
        // 显示测试结果
        function showTestResult(message) {
            const resultDiv = document.getElementById('testResult');
            const outputDiv = document.getElementById('testOutput');
            
            outputDiv.innerHTML = message;
            resultDiv.style.display = 'block';
        }
        
        // 页面加载完成后检查
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(checkFontAwesome, 1000);
        });
    </script>
</body>
</html>
