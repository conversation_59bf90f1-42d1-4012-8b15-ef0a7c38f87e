<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标测试页面</title>
    
    <!-- Font Awesome 图标库 - 使用多个CDN确保加载成功 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.5.1/css/all.min.css" crossorigin="anonymous">
    <link rel="stylesheet" href="https://unpkg.com/@fortawesome/fontawesome-free@6.5.1/css/all.min.css" crossorigin="anonymous">
    
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .icon-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .icon-item i {
            font-size: 24px;
            margin-right: 15px;
            color: #3b82f6;
            width: 30px;
            text-align: center;
        }
        
        .icon-name {
            font-weight: 500;
            color: #333;
        }
        
        .status {
            padding: 10px;
            margin: 20px 0;
            border-radius: 5px;
            font-weight: 500;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        
        .status.warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fbbf24;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .font-test {
            font-size: 18px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-bug"></i> 图标显示测试页面</h1>
        
        <div id="status" class="status">
            <i class="fas fa-spinner fa-spin"></i> 正在检测图标加载状态...
        </div>
        
        <div class="test-section">
            <h3>常用图标测试</h3>
            <div class="icon-grid">
                <div class="icon-item">
                    <i class="fas fa-industry"></i>
                    <span class="icon-name">工厂图标</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-chart-line"></i>
                    <span class="icon-name">图表图标</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-search"></i>
                    <span class="icon-name">搜索图标</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-cogs"></i>
                    <span class="icon-name">设置图标</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-users"></i>
                    <span class="icon-name">用户图标</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-microchip"></i>
                    <span class="icon-name">芯片图标</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-tools"></i>
                    <span class="icon-name">工具图标</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-award"></i>
                    <span class="icon-name">奖励图标</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-boxes"></i>
                    <span class="icon-name">箱子图标</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-check-circle"></i>
                    <span class="icon-name">检查图标</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-clock"></i>
                    <span class="icon-name">时钟图标</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-fire"></i>
                    <span class="icon-name">火焰图标</span>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>字体加载检测</h3>
            <div class="font-test">
                <strong>Font Awesome 字体族检测：</strong>
                <span id="fontFamily">检测中...</span>
            </div>
            <div class="font-test">
                <strong>CSS 加载状态：</strong>
                <span id="cssStatus">检测中...</span>
            </div>
            <div class="font-test">
                <strong>网络连接状态：</strong>
                <span id="networkStatus">检测中...</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>解决方案建议</h3>
            <div id="solutions">
                <p>正在分析问题...</p>
            </div>
        </div>
    </div>

    <script>
        // 检测Font Awesome加载状态
        function detectFontAwesome() {
            const statusDiv = document.getElementById('status');
            const fontFamilySpan = document.getElementById('fontFamily');
            const cssStatusSpan = document.getElementById('cssStatus');
            const networkStatusSpan = document.getElementById('networkStatus');
            const solutionsDiv = document.getElementById('solutions');
            
            // 检查字体族
            const testElement = document.createElement('i');
            testElement.className = 'fas fa-home';
            testElement.style.position = 'absolute';
            testElement.style.left = '-9999px';
            document.body.appendChild(testElement);
            
            const computedStyle = window.getComputedStyle(testElement, ':before');
            const fontFamily = computedStyle.getPropertyValue('font-family');
            
            document.body.removeChild(testElement);
            
            fontFamilySpan.textContent = fontFamily;
            
            // 检查CSS加载
            const links = document.querySelectorAll('link[href*="font-awesome"]');
            let cssLoaded = false;
            
            links.forEach(link => {
                if (link.sheet) {
                    cssLoaded = true;
                }
            });
            
            cssStatusSpan.textContent = cssLoaded ? '✅ CSS已加载' : '❌ CSS加载失败';
            
            // 检查网络状态
            networkStatusSpan.textContent = navigator.onLine ? '✅ 网络连接正常' : '❌ 网络连接异常';
            
            // 综合判断
            const isFontAwesomeWorking = fontFamily.includes('Font Awesome') || fontFamily.includes('FontAwesome');
            
            if (isFontAwesomeWorking) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '<i class="fas fa-check-circle"></i> Font Awesome 图标加载成功！';
                solutionsDiv.innerHTML = '<p style="color: #065f46;">✅ 图标系统工作正常，无需额外操作。</p>';
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Font Awesome 图标加载失败！';
                
                let solutions = '<h4>可能的解决方案：</h4><ul>';
                
                if (!navigator.onLine) {
                    solutions += '<li>检查网络连接</li>';
                }
                
                if (!cssLoaded) {
                    solutions += '<li>CSS文件加载失败，尝试刷新页面</li>';
                    solutions += '<li>检查防火墙或广告拦截器设置</li>';
                }
                
                solutions += '<li>尝试使用本地Font Awesome文件</li>';
                solutions += '<li>使用备用图标方案（emoji或SVG）</li>';
                solutions += '<li>检查浏览器控制台错误信息</li>';
                solutions += '</ul>';
                
                solutionsDiv.innerHTML = solutions;
            }
        }
        
        // 页面加载完成后检测
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(detectFontAwesome, 1000);
            });
        } else {
            setTimeout(detectFontAwesome, 1000);
        }
        
        // 添加刷新按钮
        const refreshBtn = document.createElement('button');
        refreshBtn.textContent = '🔄 重新检测';
        refreshBtn.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 10px 20px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        `;
        refreshBtn.onclick = () => {
            location.reload();
        };
        document.body.appendChild(refreshBtn);
    </script>
</body>
</html>
