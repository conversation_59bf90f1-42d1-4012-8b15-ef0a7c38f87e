// 文档内容模块 - 包含实际的Markdown文档内容
export const docsContent = {
  tech: `# 生产回溯系统技术实现方案

## 文档说明
本文档为《汽车零部件生产方案》的技术实现补充文档，详细说明生产回溯系统的技术架构、数据库设计、接口规范等技术细节，供开发团队参考使用。

## 数据库设计

### 数据表结构设计

#### 主表：总成数据表 (Assembly_Master)
| 字段名 | 数据类型 | 说明 | 约束 |
|--------|---------|------|------|
| assembly_id | VARCHAR(50) | 总成激光打标ID | 主键, NOT NULL |
| part1_id | VARCHAR(50) | 零件1 ID | 外键, NOT NULL |
| part2_id | VARCHAR(50) | 零件2 ID | 外键, NOT NULL |
| assembly_time | DATETIME | 组装时间 | NOT NULL |
| assembly_operator | VARCHAR(50) | 组装操作员 | NOT NULL |
| laser_marking_time | DATETIME | 激光打标时间 | NOT NULL |
| airtightness_result | DECIMAL(10,3) | 气密性检测结果 | |
| airtightness_standard | DECIMAL(10,3) | 气密性标准值 | |
| airtightness_status | VARCHAR(20) | 气密性检测状态 | DEFAULT 'PENDING' |
| final_quality_status | VARCHAR(20) | 最终质量状态 | DEFAULT 'PENDING' |
| production_line | VARCHAR(50) | 生产线编号 | NOT NULL |
| shift_id | VARCHAR(20) | 班次 | |
| create_time | DATETIME | 记录创建时间 | DEFAULT CURRENT_TIMESTAMP |
| update_time | DATETIME | 记录更新时间 | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

#### 零件1数据表 (Part1_Details)
| 字段名 | 数据类型 | 说明 | 约束 |
|--------|---------|------|------|
| part1_id | VARCHAR(50) | 零件1 ID | 主键, NOT NULL |
| material_batch | VARCHAR(50) | 原料批次号 | NOT NULL |
| stacking_operator | VARCHAR(50) | 叠放操作员 | |
| stacking_time | DATETIME | 叠放时间 | |
| vision_check_result | VARCHAR(20) | 视觉检测结果 | |
| vision_check_time | DATETIME | 视觉检测时间 | |
| vision_check_score | DECIMAL(5,2) | 视觉检测评分 | |
| pressing_pressure | DECIMAL(10,2) | 压制压力值(MPa) | |
| pressing_time | DATETIME | 压制时间 | |
| pressing_duration | INT | 压制持续时间(秒) | |
| welding_temperature | DECIMAL(10,2) | 焊接温度(°C) | |
| welding_current | DECIMAL(10,2) | 焊接电流(A) | |
| welding_time | DATETIME | 焊接时间 | |
| welding_duration | INT | 焊接持续时间(秒) | |
| airtightness_result | DECIMAL(10,3) | 零件气密性检测结果 | |
| airtightness_standard | DECIMAL(10,3) | 零件气密性标准值 | |
| airtightness_status | VARCHAR(20) | 零件气密性检测状态 | DEFAULT 'PENDING' |
| airtightness_time | DATETIME | 零件气密性检测时间 | |
| height_measurement | DECIMAL(10,3) | 高度检测值(mm) | |
| height_standard | DECIMAL(10,3) | 高度标准值(mm) | |
| height_tolerance | DECIMAL(10,3) | 高度公差(mm) | |
| height_deviation | DECIMAL(10,3) | 高度偏差(mm) | |
| flatness_measurement | DECIMAL(10,3) | 平整性检测值(mm) | |
| flatness_standard | DECIMAL(10,3) | 平整性标准值(mm) | |
| flatness_tolerance | DECIMAL(10,3) | 平整性公差(mm) | |
| flatness_deviation | DECIMAL(10,3) | 平整性偏差(mm) | |
| geometry_check_time | DATETIME | 几何检测时间 | |
| geometry_status | VARCHAR(20) | 几何检测状态 | DEFAULT 'PENDING' |
| laser_marking_time | DATETIME | 激光打标时间 | NOT NULL |
| quality_status | VARCHAR(20) | 零件质量状态 | DEFAULT 'PENDING' |
| create_time | DATETIME | 记录创建时间 | DEFAULT CURRENT_TIMESTAMP |
| update_time | DATETIME | 记录更新时间 | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

#### 零件2数据表 (Part2_Details)
| 字段名 | 数据类型 | 说明 | 约束 |
|--------|---------|------|------|
| part2_id | VARCHAR(50) | 零件2 ID | 主键, NOT NULL |
| material_batch | VARCHAR(50) | 原料批次号 | NOT NULL |
| laser_marking_time | DATETIME | 激光打标时间 | NOT NULL |
| screw1_torque | DECIMAL(10,3) | 螺丝1扭矩值(N·m) | |
| screw1_torque_time | DATETIME | 螺丝1安装时间 | |
| screw1_status | VARCHAR(20) | 螺丝1状态 | DEFAULT 'PENDING' |
| screw1_angle | DECIMAL(10,2) | 螺丝1转角(度) | |
| screw2_torque | DECIMAL(10,3) | 螺丝2扭矩值(N·m) | |
| screw2_torque_time | DATETIME | 螺丝2安装时间 | |
| screw2_status | VARCHAR(20) | 螺丝2状态 | DEFAULT 'PENDING' |
| screw2_angle | DECIMAL(10,2) | 螺丝2转角(度) | |
| screw3_torque | DECIMAL(10,3) | 螺丝3扭矩值(N·m) | |
| screw3_torque_time | DATETIME | 螺丝3安装时间 | |
| screw3_status | VARCHAR(20) | 螺丝3状态 | DEFAULT 'PENDING' |
| screw3_angle | DECIMAL(10,2) | 螺丝3转角(度) | |
| torque_standard_min | DECIMAL(10,3) | 扭矩标准下限(N·m) | NOT NULL |
| torque_standard_max | DECIMAL(10,3) | 扭矩标准上限(N·m) | NOT NULL |
| air_switch_install_time | DATETIME | 空气开关安装时间 | |
| air_switch_status | VARCHAR(20) | 空气开关状态 | DEFAULT 'PENDING' |
| air_switch_test_result | VARCHAR(20) | 空气开关测试结果 | |
| quality_status | VARCHAR(20) | 零件质量状态 | DEFAULT 'PENDING' |
| create_time | DATETIME | 记录创建时间 | DEFAULT CURRENT_TIMESTAMP |
| update_time | DATETIME | 记录更新时间 | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

### 索引设计

\`\`\`sql
-- 主表索引
CREATE INDEX idx_assembly_time ON Assembly_Master(assembly_time);
CREATE INDEX idx_assembly_line ON Assembly_Master(production_line);
CREATE INDEX idx_assembly_status ON Assembly_Master(final_quality_status);
CREATE INDEX idx_assembly_shift ON Assembly_Master(shift_id);

-- 零件1表索引
CREATE INDEX idx_part1_material ON Part1_Details(material_batch);
CREATE INDEX idx_part1_quality ON Part1_Details(quality_status);
CREATE INDEX idx_part1_geometry ON Part1_Details(geometry_status);
CREATE INDEX idx_part1_time ON Part1_Details(laser_marking_time);

-- 零件2表索引
CREATE INDEX idx_part2_material ON Part2_Details(material_batch);
CREATE INDEX idx_part2_quality ON Part2_Details(quality_status);
CREATE INDEX idx_part2_time ON Part2_Details(laser_marking_time);

-- 外键索引
CREATE INDEX idx_assembly_part1 ON Assembly_Master(part1_id);
CREATE INDEX idx_assembly_part2 ON Assembly_Master(part2_id);
\`\`\`

### 数据约束和触发器

\`\`\`sql
-- 添加外键约束
ALTER TABLE Assembly_Master 
ADD CONSTRAINT fk_assembly_part1 
FOREIGN KEY (part1_id) REFERENCES Part1_Details(part1_id);

ALTER TABLE Assembly_Master 
ADD CONSTRAINT fk_assembly_part2 
FOREIGN KEY (part2_id) REFERENCES Part2_Details(part2_id);

-- 检查约束
ALTER TABLE Part1_Details 
ADD CONSTRAINT chk_height_deviation 
CHECK (ABS(height_deviation) <= height_tolerance);

ALTER TABLE Part1_Details 
ADD CONSTRAINT chk_flatness_deviation 
CHECK (ABS(flatness_deviation) <= flatness_tolerance);

ALTER TABLE Part2_Details 
ADD CONSTRAINT chk_torque_range 
CHECK (screw1_torque BETWEEN torque_standard_min AND torque_standard_max);

-- 自动计算偏差的触发器
DELIMITER //
CREATE TRIGGER tr_part1_calculate_deviation 
BEFORE INSERT OR UPDATE ON Part1_Details
FOR EACH ROW
BEGIN
    SET NEW.height_deviation = NEW.height_measurement - NEW.height_standard;
    SET NEW.flatness_deviation = NEW.flatness_measurement - NEW.flatness_standard;
END//
DELIMITER ;
\`\`\`

## 核心SQL查询语句

### 基础查询

#### 1. 总成基本信息查询
\`\`\`sql
SELECT 
    assembly_id,
    part1_id,
    part2_id,
    assembly_time,
    assembly_operator,
    airtightness_result,
    airtightness_standard,
    final_quality_status,
    production_line,
    shift_id
FROM Assembly_Master 
WHERE assembly_id = ?;
\`\`\`

#### 2. 零件1完整数据查询
\`\`\`sql
SELECT 
    a.assembly_id,
    p1.part1_id,
    p1.material_batch,
    p1.stacking_operator,
    p1.stacking_time,
    p1.vision_check_result,
    p1.vision_check_score,
    p1.pressing_pressure,
    p1.pressing_time,
    p1.welding_temperature,
    p1.welding_current,
    p1.welding_time,
    p1.airtightness_result,
    p1.airtightness_standard,
    p1.airtightness_status,
    p1.airtightness_time,
    p1.height_measurement,
    p1.height_standard,
    p1.height_deviation,
    p1.height_tolerance,
    p1.flatness_measurement,
    p1.flatness_standard,
    p1.flatness_deviation,
    p1.flatness_tolerance,
    p1.geometry_status,
    p1.quality_status
FROM Assembly_Master a
JOIN Part1_Details p1 ON a.part1_id = p1.part1_id
WHERE a.assembly_id = ?;
\`\`\`

#### 3. 零件2完整数据查询
\`\`\`sql
SELECT 
    a.assembly_id,
    p2.part2_id,
    p2.material_batch,
    p2.screw1_torque,
    p2.screw1_status,
    p2.screw1_angle,
    p2.screw2_torque,
    p2.screw2_status,
    p2.screw2_angle,
    p2.screw3_torque,
    p2.screw3_status,
    p2.screw3_angle,
    p2.torque_standard_min,
    p2.torque_standard_max,
    p2.air_switch_status,
    p2.air_switch_test_result,
    p2.quality_status
FROM Assembly_Master a
JOIN Part2_Details p2 ON a.part2_id = p2.part2_id
WHERE a.assembly_id = ?;
\`\`\`

#### 4. 全量数据综合查询
\`\`\`sql
SELECT 
    a.assembly_id,
    a.assembly_time,
    a.assembly_operator,
    a.airtightness_result,
    a.airtightness_standard,
    a.final_quality_status,
    
    -- 零件1数据
    p1.part1_id,
    p1.material_batch as part1_material_batch,
    p1.height_measurement,
    p1.height_standard,
    p1.height_deviation,
    p1.flatness_measurement,
    p1.flatness_standard,
    p1.flatness_deviation,
    p1.geometry_status,
    p1.quality_status as part1_quality_status,
    
    -- 零件2数据
    p2.part2_id,
    p2.material_batch as part2_material_batch,
    p2.screw1_torque,
    p2.screw2_torque,
    p2.screw3_torque,
    p2.air_switch_status,
    p2.quality_status as part2_quality_status
    
FROM Assembly_Master a
JOIN Part1_Details p1 ON a.part1_id = p1.part1_id
JOIN Part2_Details p2 ON a.part2_id = p2.part2_id
WHERE a.assembly_id = ?;
\`\`\`

### 统计分析查询

#### 5. 质量统计查询
\`\`\`sql
-- 按日期统计质量状态
SELECT 
    DATE(assembly_time) as production_date,
    COUNT(*) as total_count,
    SUM(CASE WHEN final_quality_status = 'PASS' THEN 1 ELSE 0 END) as pass_count,
    SUM(CASE WHEN final_quality_status = 'FAIL' THEN 1 ELSE 0 END) as fail_count,
    ROUND(SUM(CASE WHEN final_quality_status = 'PASS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as pass_rate
FROM Assembly_Master 
WHERE assembly_time >= ? AND assembly_time <= ?
GROUP BY DATE(assembly_time)
ORDER BY production_date;
\`\`\`

#### 6. 缺陷分析查询
\`\`\`sql
-- 零件1几何缺陷分析
SELECT 
    COUNT(*) as total_count,
    SUM(CASE WHEN ABS(height_deviation) > height_tolerance THEN 1 ELSE 0 END) as height_defects,
    SUM(CASE WHEN ABS(flatness_deviation) > flatness_tolerance THEN 1 ELSE 0 END) as flatness_defects,
    AVG(height_deviation) as avg_height_deviation,
    AVG(flatness_deviation) as avg_flatness_deviation
FROM Part1_Details 
WHERE laser_marking_time >= ? AND laser_marking_time <= ?;
\`\`\`

#### 7. 扭矩分析查询
\`\`\`sql
-- 零件2扭矩分析
SELECT 
    AVG(screw1_torque) as avg_screw1_torque,
    AVG(screw2_torque) as avg_screw2_torque,
    AVG(screw3_torque) as avg_screw3_torque,
    STDDEV(screw1_torque) as std_screw1_torque,
    STDDEV(screw2_torque) as std_screw2_torque,
    STDDEV(screw3_torque) as std_screw3_torque,
    COUNT(*) as total_count
FROM Part2_Details 
WHERE laser_marking_time >= ? AND laser_marking_time <= ?
  AND quality_status = 'PASS';
\`\`\`

### 批次追溯查询

#### 8. 原料批次追溯
\`\`\`sql
-- 根据原料批次查询所有相关产品
SELECT DISTINCT
    a.assembly_id,
    a.assembly_time,
    a.final_quality_status,
    'Part1' as source_part,
    p1.material_batch
FROM Assembly_Master a
JOIN Part1_Details p1 ON a.part1_id = p1.part1_id
WHERE p1.material_batch = ?

UNION

SELECT DISTINCT
    a.assembly_id,
    a.assembly_time,
    a.final_quality_status,
    'Part2' as source_part,
    p2.material_batch
FROM Assembly_Master a
JOIN Part2_Details p2 ON a.part2_id = p2.part2_id
WHERE p2.material_batch = ?

ORDER BY assembly_time;
\`\`\`

## 数据采集接口规范

### 设备通信协议

#### 1. Modbus协议配置
\`\`\`json
{
    "modbus_config": {
        "protocol": "TCP",
        "ip_address": "*************",
        "port": 502,
        "slave_id": 1,
        "registers": {
            "height_measurement": {
                "address": 40001,
                "type": "HOLDING_REGISTER",
                "data_type": "FLOAT32",
                "scale_factor": 0.001
            },
            "flatness_measurement": {
                "address": 40003,
                "type": "HOLDING_REGISTER",
                "data_type": "FLOAT32",
                "scale_factor": 0.001
            },
            "measurement_status": {
                "address": 40005,
                "type": "HOLDING_REGISTER",
                "data_type": "UINT16"
            }
        }
    }
}
\`\`\`

#### 2. OPC-UA配置
\`\`\`json
{
    "opcua_config": {
        "endpoint": "opc.tcp://*************:4840",
        "security_policy": "None",
        "security_mode": "None",
        "namespace": "http://example.com/machinery",
        "nodes": {
            "torque_screw1": "ns=2;s=TorqueStation.Screw1.TorqueValue",
            "torque_screw2": "ns=2;s=TorqueStation.Screw2.TorqueValue", 
            "torque_screw3": "ns=2;s=TorqueStation.Screw3.TorqueValue",
            "station_status": "ns=2;s=TorqueStation.Status"
        }
    }
}
\`\`\`

### 数据传输格式

#### 3. JSON数据格式标准
\`\`\`json
{
    "message_header": {
        "message_id": "uuid",
        "timestamp": "2024-01-01T12:00:00.000Z",
        "source_device": "device_id",
        "message_type": "measurement_data"
    },
    "payload": {
        "part_id": "P1240101001",
        "measurement_type": "geometry_check",
        "measurements": {
            "height": {
                "value": 25.123,
                "unit": "mm",
                "tolerance": 0.1,
                "status": "PASS"
            },
            "flatness": {
                "value": 0.023,
                "unit": "mm", 
                "tolerance": 0.05,
                "status": "PASS"
            }
        },
        "quality_status": "PASS"
    }
}
\`\`\`

### API接口定义

#### 4. 数据采集API
\`\`\`yaml
openapi: 3.0.0
info:
  title: 生产回溯系统API
  version: 1.0.0

paths:
  /api/v1/data/part1:
    post:
      summary: 上传零件1生产数据
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                part_id:
                  type: string
                  description: 零件ID
                stage:
                  type: string
                  enum: [stacking, vision_check, pressing, welding, geometry_check, laser_marking]
                data:
                  type: object
                  description: 阶段相关数据
      responses:
        '200':
          description: 成功
        '400':
          description: 数据格式错误
        '500':
          description: 服务器错误

  /api/v1/data/part2:
    post:
      summary: 上传零件2生产数据
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                part_id:
                  type: string
                stage:
                  type: string
                  enum: [laser_marking, torque_check, air_switch_install]
                data:
                  type: object
      responses:
        '200':
          description: 成功

  /api/v1/data/assembly:
    post:
      summary: 上传总成数据
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                assembly_id:
                  type: string
                part1_id:
                  type: string
                part2_id:
                  type: string
                stage:
                  type: string
                  enum: [assembly, laser_marking, airtightness_test]
                data:
                  type: object
      responses:
        '200':
          description: 成功

  /api/v1/trace/{assembly_id}:
    get:
      summary: 查询总成追溯数据
      parameters:
        - name: assembly_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 返回完整追溯数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  assembly_info:
                    type: object
                  part1_info:
                    type: object
                  part2_info:
                    type: object
\`\`\`

## 系统架构

### 微服务架构设计

\`\`\`mermaid
graph TB
    subgraph "设备层"
        A1[视觉检测设备]
        A2[几何检测设备]
        A3[力矩检测设备]
        A4[激光打标设备]
        A5[气密性检测设备]
    end
    
    subgraph "数据采集层"
        B1[Modbus网关]
        B2[OPC-UA客户端]
        B3[TCP/IP接口]
    end
    
    subgraph "应用服务层"
        C1[数据采集服务]
        C2[数据处理服务]
        C3[追溯查询服务]
        C4[报表服务]
    end
    
    subgraph "数据存储层"
        D1[主数据库<br/>MySQL]
        D2[时序数据库<br/>InfluxDB]
        D3[缓存层<br/>Redis]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B2
    A4 --> B3
    A5 --> B3
    
    B1 --> C1
    B2 --> C1
    B3 --> C1
    
    C1 --> C2
    C2 --> D1
    C2 --> D2
    C3 --> D1
    C3 --> D3
    C4 --> D1
    C4 --> D2
\`\`\`

### 部署配置

#### Docker Compose配置
\`\`\`yaml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: production_trace
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  influxdb:
    image: influxdb:2.7
    environment:
      INFLUXDB_DB: production_metrics
      INFLUXDB_ADMIN_USER: admin
      INFLUXDB_ADMIN_PASSWORD: admin_password
    ports:
      - "8086:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2

  data_collector:
    build: ./services/data-collector
    environment:
      DB_HOST: mysql
      DB_USER: root
      DB_PASSWORD: root_password
      REDIS_HOST: redis
      INFLUX_HOST: influxdb
    depends_on:
      - mysql
      - redis
      - influxdb

  trace_service:
    build: ./services/trace-service
    environment:
      DB_HOST: mysql
      REDIS_HOST: redis
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis

volumes:
  mysql_data:
  redis_data:
  influxdb_data:
\`\`\`

## 性能优化

### 数据库优化策略

1. **分表策略**
\`\`\`sql
-- 按月分表
CREATE TABLE Assembly_Master_202401 LIKE Assembly_Master;
CREATE TABLE Assembly_Master_202402 LIKE Assembly_Master;
-- 使用存储过程自动路由到对应月份的表
\`\`\`

2. **读写分离**
\`\`\`yaml
database:
  master:
    host: db-master
    port: 3306
  slaves:
    - host: db-slave1
      port: 3306
    - host: db-slave2
      port: 3306
\`\`\`

3. **查询缓存**
\`\`\`python
# Redis缓存策略
def get_trace_data(assembly_id):
    cache_key = f"trace:{assembly_id}"
    cached_data = redis.get(cache_key)
    
    if cached_data:
        return json.loads(cached_data)
    
    # 从数据库查询
    data = query_from_database(assembly_id)
    
    # 缓存1小时
    redis.setex(cache_key, 3600, json.dumps(data))
    
    return data
\`\`\`

## 监控和运维

### 监控指标

1. **系统指标**
   - 数据采集成功率
   - API响应时间
   - 数据库连接池使用率
   - 缓存命中率

2. **业务指标**
   - 每日生产数量
   - 质量合格率
   - 数据完整性
   - 追溯查询频率

### 日志规范

\`\`\`json
{
    "timestamp": "2024-01-01T12:00:00.000Z",
    "level": "INFO",
    "service": "data-collector",
    "trace_id": "uuid",
    "message": "Data collected successfully",
    "context": {
        "device_id": "DEV001",
        "part_id": "P1240101001",
        "data_type": "geometry_measurement"
    }
}
\`\`\`

## 安全配置

### 数据库安全
\`\`\`sql
-- 创建专用用户
CREATE USER 'trace_user'@'%' IDENTIFIED BY 'secure_password';
GRANT SELECT, INSERT, UPDATE ON production_trace.* TO 'trace_user'@'%';

-- 敏感数据加密
ALTER TABLE Part1_Details ADD COLUMN operator_encrypted VARBINARY(255);
\`\`\`

### API安全
\`\`\`yaml
security:
  jwt:
    secret: \${JWT_SECRET}
    expiration: 86400
  rate_limit:
    requests_per_minute: 100
  cors:
    allowed_origins: 
      - http://localhost:3000
      - https://production-dashboard.company.com
\`\`\`

这个技术文档涵盖了生产回溯系统的所有技术实现细节，为开发团队提供了完整的技术指导。`,

  production: `# 汽车零部件生产方案

## 概述
本方案为客户设计两条生产线，生产汽车散热系统相关零部件，包括散热件生产、零件组装和总成装配。

## 系统架构

### 微服务架构设计

\`\`\`mermaid
graph TB
    subgraph "设备层"
        A1[视觉检测设备]
        A2[几何检测设备]
        A3[力矩检测设备]
        A4[激光打标设备]
        A5[气密性检测设备]
    end
    
    subgraph "数据采集层"
        B1[Modbus网关]
        B2[OPC-UA客户端]
        B3[TCP/IP接口]
    end
    
    subgraph "应用服务层"
        C1[数据采集服务]
        C2[数据处理服务]
        C3[追溯查询服务]
        C4[报表服务]
    end
    
    subgraph "数据存储层"
        D1[主数据库<br/>MySQL]
        D2[时序数据库<br/>InfluxDB]
        D3[缓存层<br/>Redis]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B2
    A4 --> B3
    A5 --> B3
    
    B1 --> C1
    B2 --> C1
    B3 --> C1
    
    C1 --> C2
    C2 --> D1
    C2 --> D2
    C3 --> D1
    C3 --> D3
    C4 --> D1
    C4 --> D2
\`\`\`

## 生产流程图

\`\`\`mermaid
graph TD
    subgraph "第一条生产线 - 散热件生产"
        A[原料散热片] --> B[人工叠放<br/>标记检查]
        B --> C[视觉识别检测<br/>标记验证]
        C --> D{检测结果}
        D -->|合格| E[机械手/传送带分流]
        D -->|不合格| F[不良品区域]
        E --> G[压机压制成型]
        G --> H[焊接工艺<br/>形成一体]
        H --> H0[零件气密性检测]
        H0 --> H01{气密性检测结果}
        H01 -->|合格| H1[高度检测]
        H01 -->|不合格| H02[不良品处理]
        H1 --> H2[平整性检测]
        H2 --> H3{检测结果}
        H3 -->|合格| I[激光打标<br/>零件ID]
        H3 -->|不合格| H4[不良品处理]
        I --> J[入库<br/>零件类型1]
    end
    
    subgraph "第二条生产线 - 零件2生产"
        K[零件2原料] --> L[激光打标<br/>记录ID]
        L --> M[三个螺丝装配<br/>力矩检测]
        M --> N[空气开关安装]
        N --> O[零件2完成]
    end
    
    subgraph "总成组装线"
        J --> P[总成组装]
        O --> P
        P --> Q[激光打标<br/>总成ID]
        Q --> R[数据关联<br/>ID对应关系]
        R --> S[总成气密性检测]
        S --> T{检测结果}
        T -->|合格| U[合格品入库]
        T -->|不合格| V[不良品处理]
    end
    
    style A fill:#e1f5fe
    style K fill:#e1f5fe
    style J fill:#c8e6c9
    style O fill:#c8e6c9
    style U fill:#a5d6a7
    style F fill:#ffcdd2
    style V fill:#ffcdd2
    style H4 fill:#ffcdd2
    style H02 fill:#ffcdd2
\`\`\`

## 生产线布局图

\`\`\`mermaid
graph LR
    subgraph "第一条生产线 - 散热件生产"
        A1[散热片原料] --> B1[叠放工位]
        B1 --> C1[视觉检测站]
        C1 --> D1[分拣机械手]
        D1 --> E1[压制工位]
        E1 --> F1[焊接工位]
        F1 --> F3[高度检测站]
        F3 --> G1[激光打标站]
        G1 --> H1[零件1仓库]
    end
    
    subgraph "第二条生产线 - 零件2生产"
        A2[零件2原料] --> B2[激光打标站]
        B2 --> C2[螺丝装配站1]
        C2 --> D2[螺丝装配站2]
        D2 --> E2[螺丝装配站3]
        E2 --> F2[空气开关安装]
        F2 --> G2[零件2完成]
    end
    
    subgraph "总成装配线"
        H1 --> I[总成组装工位]
        G2 --> I
        I --> J[总成激光打标]
        J --> K[数据关联记录]
        K --> L[气密性检测站]
        L --> M[成品仓库]
    end
    
    style H1 fill:#c8e6c9
    style G2 fill:#c8e6c9
    style M fill:#a5d6a7
\`\`\`

## 第一条生产线 - 散热件生产（零件类型1）

### 产品描述
- **产品名称**: 汽车散热件（锌件）
- **材质**: 锌合金
- **产品类型**: 零件类型1

### 生产工艺流程

#### 1. 散热片叠放
- **操作方式**: 人工操作
- **工艺要求**: 工人将散热片按层叠放
- **质量控制**: 每层侧面均有标记，用于后续识别

#### 2. 视觉识别检测
- **检测内容**: 验证每层散热片侧面标记的正确性
- **检测方式**: 视觉识别系统
- **分流方式**: 
  - 合格品：机械手分流或传送带分流至正常流程
  - 不合格品：分流至不良品区域

#### 3. 压制成型
- **设备**: 压机
- **工艺**: 将叠放的散热片压实成型

#### 4. 焊接工艺
- **目的**: 将堆叠的零件焊接成一体
- **工艺要求**: 确保焊接强度和密封性

#### 5. 气密性检测
- **检测设备**: 气密性检测装置
- **系统集成**: 检测设备与生产管理系统对接
- **数据获取**: 
  - 实时获取气密性检测数据
  - 自动判定合格/不合格
  - 数据存储与追溯

#### 6. 几何尺寸检测
- **检测项目**: 
  - 零件高度检测
  - 零件平整性检测
- **检测设备**: 
  - 高度检测量具（支持系统通讯）
  - 平整性检测量具（支持系统通讯）
- **质量控制**: 
  - 实时数据采集与记录
  - 自动合格/不合格判定
  - 不合格品自动分流处理
- **数据管理**: 
  - 检测数据与零件ID关联
  - 建立质量追溯档案

#### 7. 激光打标与入库
- **打标内容**: 零件唯一标识码
- **入库分类**: 零件类型1
- **数据记录**: 建立零件档案

## 第二条生产线 - 零件2生产

### 产品描述
- **产品名称**: 零件2
- **后续用途**: 与散热件组装成总成

### 生产工艺流程

#### 1. 激光打标
- **打标内容**: 零件ID唯一标识
- **目的**: 建立零件可追溯性

#### 2. 螺丝装配
- **螺丝数量**: 3个
- **装配设备**: 3台力矩检测螺丝枪
- **工艺要求**: 
  - 每个螺丝按指定力矩拧紧
  - 实时记录每个螺丝的拧紧力矩数据
  - 确保力矩值符合规范要求

#### 3. 空气开关安装
- **安装内容**: 空气开关组件
- **安装方式**: 人工或自动化安装

## 总成组装线

### 组装工艺

#### 1. 零件组装
- **组装对象**: 零件类型1（散热件） + 零件2
- **组装方式**: 精密装配
- **质量要求**: 确保配合精度和密封性

#### 2. 总成打标与数据记录
- **激光打标**: 
  - 总成零件唯一ID
  - 生产日期、批次等信息
- **数据关联**: 
  - 记录总成ID与子零件ID的对应关系
  - 建立完整的产品族谱

#### 3. 气密性检测
- **检测设备**: 气密性检测装置
- **系统集成**: 检测设备与生产管理系统对接
- **数据获取**: 
  - 实时获取气密性检测数据
  - 自动判定合格/不合格
  - 数据存储与追溯

## 生产回溯系统方案

### 系统概述
基于三级激光打标ID建立的全流程生产回溯系统，实现从总成到子零件的完整数据追溯链条。通过总成ID可以快速定位和查询所有相关生产数据，确保产品质量的全程可追溯性。

### 系统架构设计

#### 三级ID追溯体系
\`\`\`mermaid
graph TD
    A[总成ID - 产品唯一身份证] --> B[零件1 ID - 散热件身份证]
    A --> C[零件2 ID - 组装件身份证]
    B --> D[零件1全生产过程数据]
    C --> E[零件2全生产过程数据]
    
    subgraph "零件1可追溯数据"
        D --> D1[原料批次信息]
        D --> D2[视觉检测结果]
        D --> D3[压制工艺参数]
        D --> D4[焊接工艺数据]
        D --> D5[高度检测数据]
        D --> D6[平整性检测数据]
        D --> D7[质量状态记录]
    end
    
    subgraph "零件2可追溯数据"
        E --> E1[原料批次信息]
        E --> E2[螺丝1扭矩数据]
        E --> E3[螺丝2扭矩数据]
        E --> E4[螺丝3扭矩数据]
        E --> E5[空气开关状态]
    end
    
    subgraph "总成可追溯数据"
        A --> F[组装工艺数据]
        A --> G[气密性检测数据]
        A --> H[最终质量判定]
        A --> I[出厂检验记录]
    end
    
    style A fill:#ffeb3b
    style B fill:#4caf50
    style C fill:#2196f3
\`\`\`

#### 系统价值体现
- **一个总成ID = 完整产品档案**：输入总成ID即可获取产品从原料到成品的全部信息
- **子零件追溯**：可以追溯到每个子零件的具体生产参数和质量数据
- **质量问题快速定位**：出现质量问题时，能够快速定位到具体的生产环节和参数
- **数据驱动改进**：基于历史数据分析，持续优化生产工艺

### 数据采集流程

#### 零件1数据采集节点
1. **散热片叠放阶段**
   - 采集时间：叠放完成时
   - 数据内容：操作员、时间、原料批次
   - 采集方式：人工输入 + 自动时间戳

2. **视觉检测阶段**
   - 采集时间：检测完成时
   - 数据内容：检测结果、检测时间
   - 采集方式：视觉系统自动上传

3. **压制成型阶段**
   - 采集时间：压制完成时
   - 数据内容：压制压力、设备参数
   - 采集方式：压机设备数据接口

4. **焊接工艺阶段**
   - 采集时间：焊接完成时
   - 数据内容：焊接温度、电流、时间
   - 采集方式：焊接设备数据接口

5. **气密性检测阶段**
   - 采集时间：检测完成时
   - 数据内容：检测结果、标准对比、判定状态
   - 采集方式：检测设备通信接口

6. **几何检测阶段**
   - 采集时间：检测完成时
   - 数据内容：高度值、平整性值、公差对比
   - 采集方式：检测设备通信接口

7. **激光打标阶段**
   - 采集时间：打标完成时
   - 数据内容：零件ID、打标时间
   - 采集方式：激光设备数据接口

#### 零件2数据采集节点
1. **激光打标阶段**
   - 采集时间：打标完成时
   - 数据内容：零件ID、打标时间
   - 采集方式：激光设备数据接口

2. **螺丝装配阶段**
   - 采集时间：每个螺丝安装完成时
   - 数据内容：扭矩值、安装时间、状态判定
   - 采集方式：力矩检测螺丝枪数据接口

3. **空气开关安装阶段**
   - 采集时间：安装完成时
   - 数据内容：安装时间、状态确认
   - 采集方式：人工确认 + 自动时间戳

#### 总成数据采集节点
1. **组装阶段**
   - 采集时间：组装完成时
   - 数据内容：子零件ID关联、组装操作员、时间
   - 采集方式：扫码关联 + 人工输入

2. **激光打标阶段**
   - 采集时间：打标完成时
   - 数据内容：总成ID、打标时间
   - 采集方式：激光设备数据接口

3. **气密性检测阶段**
   - 采集时间：检测完成时
   - 数据内容：检测结果、标准对比、判定状态
   - 采集方式：检测设备通信接口

### 系统运行流程

#### 数据关联与追溯时序图
\`\`\`mermaid
sequenceDiagram
    participant P1 as 零件1生产线
    participant P2 as 零件2生产线
    participant AS as 总成装配线
    participant SYS as 回溯系统
    participant USER as 用户查询
    
    Note over P1,P2: 并行生产阶段
    P1->>SYS: 散热片叠放 → 记录操作员、批次
    P1->>SYS: 视觉检测 → 记录检测结果
    P1->>SYS: 压制成型 → 记录工艺参数
    P1->>SYS: 焊接工艺 → 记录温度、电流
    P1->>SYS: 气密性检测 → 记录检测结果
    P1->>SYS: 几何检测 → 记录高度、平整性
    P1->>SYS: 激光打标 → 生成零件1 ID
    
    P2->>SYS: 激光打标 → 生成零件2 ID
    P2->>SYS: 螺丝装配 → 记录3个扭矩值
    P2->>SYS: 空气开关安装 → 记录安装状态
    
    Note over AS,SYS: 总成装配阶段
    AS->>SYS: 扫描零件1 ID
    AS->>SYS: 扫描零件2 ID
    AS->>SYS: 生成总成ID并建立关联
    AS->>SYS: 组装完成 → 记录组装数据
    AS->>SYS: 气密性检测 → 记录检测结果
    
    Note over USER,SYS: 追溯查询阶段
    USER->>SYS: 输入总成ID查询
    SYS->>USER: 返回零件1气密性、高度、平整性数据
    SYS->>USER: 返回零件2扭矩、开关状态
    SYS->>USER: 返回总成气密性检测结果
    SYS->>USER: 提供完整生产追溯报告
\`\`\`

#### 系统智能化特点
1. **自动关联验证**：组装时系统自动验证子零件状态，确保只有合格零件进入总成
2. **实时数据采集**：生产过程中所有关键数据实时自动采集，无需人工干预
3. **智能质量判定**：系统根据预设标准自动判定每个环节的质量状态
4. **一键追溯查询**：输入总成ID即可获得完整的产品生产历程

### 追溯查询功能

#### 用户界面功能
1. **一键查询**
   - 输入总成ID，3秒内获取完整产品档案
   - 直观展示：产品族谱关系图

2. **分类数据展示**
   - **零件1追溯**：气密性检测结果、平整性检测值、高度检测值、几何检测状态
   - **零件2追溯**：三个螺丝扭矩值、空气开关状态  
   - **总成追溯**：总成气密性检测结果、最终质量判定

3. **智能分析**
   - 生产时间轴可视化展示
   - 质量数据趋势分析
   - 异常数据智能预警

#### 实际应用场景
- **质量问题排查**：客户反馈质量问题时，快速定位生产环节
- **工艺优化**：基于历史数据分析，识别工艺改进点
- **供应链管理**：追溯原料批次，评估供应商质量
- **法规合规**：满足汽车行业质量追溯要求

### 预期效果

#### 追溯效率提升
- **查询速度**：通过总成ID 3秒内获取全部相关数据
- **数据完整性**：100% 生产环节数据覆盖
- **操作便利性**：一次输入，全面展示

#### 质量管控增强
- **问题定位**：快速定位质量问题根源
- **趋势分析**：基于历史数据进行质量趋势分析
- **预防措施**：基于数据分析制定预防性措施

#### 管理价值
- **透明化管理**：生产过程全透明
- **数据驱动决策**：基于真实数据进行管理决策
- **持续改进**：数据支撑的持续改进机制

## 系统集成方案

### 生产管理系统对接
- MES系统集成
- 实时生产数据采集
- 设备状态监控
- 质量数据管理

### 设备通信
- 视觉识别系统
- 气密性检测设备（零件级）
- 高度检测量具
- 平整性检测量具
- 力矩检测设备
- 激光打标设备
- 气密性检测设备（总成级）
- 机械手/传送带控制系统

## 技术特点

### 自动化程度
- 视觉识别系统自动化质量检测
- 机械手或传送带自动化分流
- 几何尺寸自动化检测（高度+平整性）
- 力矩检测螺丝枪自动化装配
- 激光打标自动化标识

### 数据管理
- 全流程数据采集与记录
- 零件级可追溯性管理
- 总成与子零件关联管理
- 质量数据实时监控

### 质量控制
- 多点质量检测节点
- 实时数据反馈
- 不合格品自动分流
- 完整的质量追溯体系

## 预期效果

### 生产效率
- 自动化程度高，减少人工操作
- 实时质量控制，减少返工
- 数据化管理，提高生产透明度

### 质量保证
- 多重质量检测
- 全程可追溯
- 数据驱动的质量改进

### 管理优化
- 实时生产监控
- 数据化决策支持
- 精益生产管理`
}; 