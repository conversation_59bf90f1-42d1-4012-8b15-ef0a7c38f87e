# 两次气密检查工艺流程说明

## 概述
根据您的要求，我们已经将工艺流程修改为包含两次气密检查：
1. **第一次气密检查**：在零件1焊接工艺完成后进行
2. **第二次气密检查**：在模块总成后作为最后一道工序进行

## 详细工艺流程

### 第一次气密检查（零件级别）

#### 检查时机
- **位置**：第一条生产线 - 散热件生产
- **工序位置**：焊接工艺完成后，几何尺寸检测之前
- **目的**：验证焊接密封性，确保零件本身的气密性能

#### 工艺流程
```
散热片叠放 → 视觉识别检测 → 压制成型 → 焊接工艺 → 【第一次气密检查】 → 高度检测 → 平整性检测 → 激光打标 → 入库
```

#### 检测要求
- **检测设备**：气密性检测装置
- **检测标准**：零件级气密性标准值
- **数据记录**：
  - 检测结果值
  - 标准对比
  - 合格/不合格判定
  - 检测时间
- **质量控制**：
  - 不合格品自动分流至不良品处理区域
  - 合格品继续后续几何检测流程

### 第二次气密检查（总成级别）

#### 检查时机
- **位置**：总成组装线
- **工序位置**：总成组装和激光打标完成后，作为最后一道工序
- **目的**：验证总成整体的气密性能，确保最终产品质量

#### 工艺流程
```
零件组装 → 激光打标 → 数据关联 → 【第二次气密检查】 → 合格品入库/不良品处理
```

#### 检测要求
- **检测设备**：气密性检测装置
- **检测标准**：总成级气密性标准值
- **数据记录**：
  - 检测结果值
  - 标准对比
  - 合格/不合格判定
  - 检测时间
- **质量控制**：
  - 这是产品出厂前的最后一道质量检测工序
  - 不合格品进入不良品处理流程
  - 合格品方可入库出厂

## 数据库存储设计

### 零件1表（Part1_Details）新增字段
| 字段名 | 数据类型 | 说明 | 约束 |
|--------|---------|------|------|
| airtightness_result | DECIMAL(10,3) | 零件气密性检测结果 | |
| airtightness_standard | DECIMAL(10,3) | 零件气密性标准值 | |
| airtightness_status | VARCHAR(20) | 零件气密性检测状态 | DEFAULT 'PENDING' |
| airtightness_time | DATETIME | 零件气密性检测时间 | |

### 总成表（Assembly_Master）现有字段
| 字段名 | 数据类型 | 说明 | 约束 |
|--------|---------|------|------|
| airtightness_result | DECIMAL(10,3) | 总成气密性检测结果 | |
| airtightness_standard | DECIMAL(10,3) | 总成气密性标准值 | |
| airtightness_status | VARCHAR(20) | 总成气密性检测状态 | DEFAULT 'PENDING' |

## 数据追溯功能

### 查询结果包含
通过总成ID查询时，系统将返回：

1. **零件1追溯数据**：
   - 零件气密性检测结果（第一次）
   - 高度检测数据
   - 平整性检测数据
   - 几何检测状态

2. **零件2追溯数据**：
   - 三个螺丝扭矩值
   - 空气开关状态

3. **总成追溯数据**：
   - 总成气密性检测结果（第二次）
   - 最终质量判定

## 质量控制优势

### 双重保障
- **第一次检查**：确保零件本身的密封性，避免有缺陷的零件进入后续流程
- **第二次检查**：验证总成整体性能，确保组装过程没有影响气密性

### 问题定位
- 如果第一次检查合格，第二次检查不合格，说明问题出现在组装环节
- 如果第一次检查不合格，可以直接定位到焊接工艺问题

### 成本控制
- 早期发现问题，减少后续加工成本
- 避免不合格零件进入总成，降低整体损失

## 系统集成

### 设备对接
- 两套气密性检测设备分别与生产管理系统对接
- 实时数据采集和自动判定
- 自动分流控制

### 数据管理
- 两次检测数据分别存储在对应的数据表中
- 建立完整的追溯链条
- 支持历史数据查询和分析

## 实施建议

### 设备配置
- 第一条生产线：配置零件级气密性检测设备
- 总成组装线：配置总成级气密性检测设备

### 标准设定
- 建议零件级标准略严于总成级标准
- 确保零件合格的基础上，总成检测作为最终验证

### 人员培训
- 操作人员需要了解两次检测的不同目的和要求
- 质量人员需要掌握两次检测数据的分析方法

这样的双重气密检查设计，既保证了零件质量，又确保了最终产品的可靠性，形成了完整的质量保障体系。
