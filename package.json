{"name": "production-trace-system", "productName": "银轮集团生产回溯系统", "version": "1.0.2", "description": "汽车零部件生产过程追溯管理系统 - 基于三级ID的全流程数据追溯解决方案", "main": ".webpack/main", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "echo \"No linting configured\"", "web": "webpack serve --config webpack.renderer.config.js --open"}, "keywords": [], "author": "yunsong1", "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "@electron-forge/plugin-fuses": "^7.8.1", "@electron-forge/plugin-webpack": "^7.8.1", "@electron/fuses": "^1.8.0", "@vercel/webpack-asset-relocator-loader": "^1.7.3", "css-loader": "^6.11.0", "electron": "37.0.0", "node-loader": "^2.1.0", "style-loader": "^3.3.4", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "chart.js": "^4.5.0", "electron-squirrel-startup": "^1.0.1", "github-markdown-css": "^5.8.1", "highlight.js": "^11.11.1", "katex": "^0.16.22", "marked": "^15.0.12", "mermaid": "^11.7.0", "three": "^0.177.0"}}