<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>银轮机械生产回溯系统</title>
  <!-- Font Awesome 图标库 - 优先使用本地文件，CDN作为备用 -->
  <link rel="stylesheet" href="fontawesome/all.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" crossorigin="anonymous">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.5.1/css/all.min.css" crossorigin="anonymous">
  <link rel="stylesheet" href="https://unpkg.com/@fortawesome/fontawesome-free@6.5.1/css/all.min.css" crossorigin="anonymous">

  <!-- 备用图标样式 - 当Font Awesome加载失败时使用 -->
  <link rel="stylesheet" href="icons-fallback.css">

  <!-- 图标修复脚本 -->
  <script src="fix-icons.js"></script>

  <!-- 图标修复脚本 -->
  <script src="fix-icons.js"></script>
</head>

<body>
  <!-- 头部导航 -->
  <header class="header">
    <div class="header-content">
      <h1><i class="fas fa-industry"></i> 银轮机械生产回溯系统</h1>
      <div class="header-actions">
        <div class="current-time" id="currentTime"></div>
        <button class="btn btn-outline" id="docsBtn">
          <i class="fas fa-book"></i> 方案文档
        </button>
        <button class="btn btn-outline" id="helpBtn">
          <i class="fas fa-question-circle"></i> 帮助
        </button>
      </div>
    </div>
  </header>

  <!-- 主要内容区域 -->
  <main class="main-content">
    <!-- 侧边栏菜单 -->
    <aside class="sidebar">
      <nav class="nav">
        <div class="nav-item active" data-tab="dashboard">
          <i class="fas fa-chart-line"></i>
          <span>数据看板</span>
        </div>
        <div class="nav-item " data-tab="trace">
          <i class="fas fa-search"></i>
          <span>产品追溯</span>
        </div>

        <div class="nav-item" data-tab="production">
          <i class="fas fa-cogs"></i>
          <span>生产流程</span>
        </div>
        <div class="nav-item" data-tab="quality">
          <i class="fas fa-award"></i>
          <span>质量分析</span>
        </div>
        <div class="nav-item" data-tab="employee">
          <i class="fas fa-users"></i>
          <span>员工管理</span>
        </div>
        <div class="nav-item" data-tab="device">
          <i class="fas fa-microchip"></i>
          <span>设备管理</span>
        </div>
        <div class="nav-item" data-tab="screw-guide">
          <i class="fas fa-tools"></i>
          <span>3D拧螺丝指引</span>
        </div>
        <div class="nav-item" data-tab="report">
          <i class="fas fa-file-alt"></i>
          <span>报表中心</span>
        </div>
      </nav>
    </aside>

    <!-- 内容区域 -->
    <section class="content">


      <!-- 数据看板页面 -->
      <div class="tab-content active" id="dashboard-tab">
        <div class="content-header">
          <h2><i class="fas fa-chart-line"></i> 生产数据看板</h2>
          <p>实时生产数据监控与分析</p>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
          <div class="filter-group">
            <label>时间范围:</label>
            <select id="dashboardTimeRange">
              <option value="today">今天</option>
              <option value="week">本周</option>
              <option value="month">本月</option>
              <option value="custom">自定义</option>
            </select>
          </div>
          <div class="filter-group">
            <label>生产线:</label>
            <select id="dashboardProductionLine">
              <option value="all">全部</option>
              <option value="Line-01">Line-01</option>
              <option value="Line-02">Line-02</option>
              <option value="Line-03">Line-03</option>
            </select>
          </div>
          <div class="filter-group">
            <label>班次:</label>
            <select id="dashboardShift">
              <option value="all">全部</option>
              <option value="Day-Shift">白班</option>
              <option value="Night-Shift">夜班</option>
            </select>
          </div>
          <div class="filter-group" id="customDateRange" style="display: none;">
            <label>开始日期:</label>
            <input type="date" id="startDate">
            <label>结束日期:</label>
            <input type="date" id="endDate">
          </div>
          <button class="btn btn-primary" id="applyDashboardFilter">
            <i class="fas fa-filter"></i> 应用筛选
          </button>
        </div>

        <div class="dashboard-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-boxes"></i>
            </div>
            <div class="stat-content">
              <h3>产量</h3>
              <div class="stat-number" id="productionCount">1,245</div>
              <div class="stat-change positive" id="productionChange">+12.5%</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-content">
              <h3>合格率</h3>
              <div class="stat-number" id="qualityRate">98.7%</div>
              <div class="stat-change positive" id="qualityChange">+0.3%</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
              <h3>平均节拍</h3>
              <div class="stat-number" id="avgCycleTime">45s</div>
              <div class="stat-change negative" id="cycleTimeChange">-2s</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-microchip"></i>
            </div>
            <div class="stat-content">
              <h3>在线设备</h3>
              <div class="stat-number" id="onlineDevices">24/26</div>
              <div class="stat-change positive" id="deviceChange">+1</div>
            </div>
          </div>
        </div>

        <div class="chart-section">
          <div class="card">
            <div class="card-header">
              <h3>生产趋势</h3>
            </div>
            <div class="card-body">
              <canvas id="productionChart"></canvas>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3>质量分布</h3>
            </div>
            <div class="card-body">
              <canvas id="qualityChart"></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- 产品追溯页面 -->
      <div class="tab-content " id="trace-tab">
        <div class="content-header">
          <h2><i class="fas fa-search"></i> 产品追溯查询</h2>
          <p>通过总成ID快速查询产品完整生产档案</p>
        </div>

        <!-- 查询区域 -->
        <div class="search-section">
          <div class="search-box">
            <label for="assemblyId">总成ID</label>
            <div class="input-group">
              <input type="text" id="assemblyId" placeholder="请输入总成ID，例如：ASM240101001" />
              <button class="btn btn-primary" id="searchBtn">
                <i class="fas fa-search"></i> 查询
              </button>
            </div>
          </div>
        </div>

        <!-- 结果展示区域 -->
        <div class="result-section" id="resultSection" style="display: none;">
          <!-- 总成信息卡片 -->
          <div class="card">
            <div class="card-header">
              <h3><i class="fas fa-cube"></i> 总成信息</h3>
            </div>
            <div class="card-body" id="assemblyInfo">
              <!-- 动态内容 -->
            </div>
          </div>

          <!-- 三级数据展示 -->
          <div class="data-grid">
            <!-- 零件1信息 -->
            <div class="card">
              <div class="card-header">
                <h3><i class="fas fa-puzzle-piece"></i> 零件1 - 散热件</h3>
              </div>
              <div class="card-body" id="part1Info">
                <!-- 动态内容 -->
              </div>
            </div>

            <!-- 零件2信息 -->
            <div class="card">
              <div class="card-header">
                <h3><i class="fas fa-puzzle-piece"></i> 零件2 - 组装件</h3>
              </div>
              <div class="card-body" id="part2Info">
                <!-- 动态内容 -->
              </div>
            </div>
          </div>

          <!-- 生产时间轴 -->
          <div class="card">
            <div class="card-header">
              <h3><i class="fas fa-clock"></i> 生产时间轴</h3>
            </div>
            <div class="card-body">
              <div class="timeline" id="productionTimeline">
                <!-- 动态内容 -->
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 生产流程页面 -->
      <div class="tab-content" id="production-tab">
        <div class="content-header">
          <h2><i class="fas fa-cogs"></i> 生产流程监控</h2>
          <p>实时监控生产线状态和工艺参数</p>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
          <div class="filter-group">
            <label>生产线:</label>
            <select id="productionLineFilter">
              <option value="all">全部</option>
              <option value="Line-01">Line-01</option>
              <option value="Line-02">Line-02</option>
              <option value="Line-03">Line-03</option>
            </select>
          </div>
          <div class="filter-group">
            <label>状态:</label>
            <select id="productionStatusFilter">
              <option value="all">全部</option>
              <option value="running">运行中</option>
              <option value="idle">待机</option>
              <option value="maintenance">维护中</option>
              <option value="error">故障</option>
            </select>
          </div>
          <button class="btn btn-primary" id="applyProductionFilter">
            <i class="fas fa-sync"></i> 刷新状态
          </button>
        </div>

        <div class="process-diagram">
          <div class="process-step">
            <div class="step-icon">
              <i class="fas fa-layer-group"></i>
            </div>
            <h4>叠合</h4>
            <div class="status online">正常运行</div>
            <div class="step-details">
              <div class="detail-item">
                <span>当前操作员:</span>
                <span>陈工</span>
              </div>
              <div class="detail-item">
                <span>当前产量:</span>
                <span>245件</span>
              </div>
              <div class="detail-item">
                <span>节拍时间:</span>
                <span>42s</span>
              </div>
            </div>
          </div>

          <div class="process-arrow">
            <i class="fas fa-arrow-right"></i>
          </div>

          <div class="process-step">
            <div class="step-icon">
              <i class="fas fa-compress-arrows-alt"></i>
            </div>
            <h4>压合</h4>
            <div class="status online">正常运行</div>
            <div class="step-details">
              <div class="detail-item">
                <span>压力设定:</span>
                <span>125.0±2.0 bar</span>
              </div>
              <div class="detail-item">
                <span>当前压力:</span>
                <span>125.8 bar</span>
              </div>
              <div class="detail-item">
                <span>压合次数:</span>
                <span>245次</span>
              </div>
            </div>
          </div>

          <div class="process-arrow">
            <i class="fas fa-arrow-right"></i>
          </div>

          <div class="process-step">
            <div class="step-icon">
              <i class="fas fa-fire"></i>
            </div>
            <h4>焊接</h4>
            <div class="status warning">参数异常</div>
            <div class="step-details">
              <div class="detail-item">
                <span>温度设定:</span>
                <span>285±5°C</span>
              </div>
              <div class="detail-item">
                <span>当前温度:</span>
                <span class="warning-value">291°C</span>
              </div>
              <div class="detail-item">
                <span>焊接次数:</span>
                <span>242次</span>
              </div>
            </div>
          </div>

          <div class="process-arrow">
            <i class="fas fa-arrow-right"></i>
          </div>

          <div class="process-step">
            <div class="step-icon">
              <i class="fas fa-wind"></i>
            </div>
            <h4>气密性检测</h4>
            <div class="status online">正常运行</div>
            <div class="step-details">
              <div class="detail-item">
                <span>标准值:</span>
                <span>≤0.030 MPa·l/min</span>
              </div>
              <div class="detail-item">
                <span>平均值:</span>
                <span>0.025 MPa·l/min</span>
              </div>
              <div class="detail-item">
                <span>合格率:</span>
                <span>98.3%</span>
              </div>
            </div>
          </div>

          <div class="process-arrow">
            <i class="fas fa-arrow-right"></i>
          </div>

          <div class="process-step">
            <div class="step-icon">
              <i class="fas fa-ruler"></i>
            </div>
            <h4>几何检测</h4>
            <div class="status online">正常运行</div>
            <div class="step-details">
              <div class="detail-item">
                <span>高度检测:</span>
                <span>合格率 99.2%</span>
              </div>
              <div class="detail-item">
                <span>平面度检测:</span>
                <span>合格率 98.8%</span>
              </div>
              <div class="detail-item">
                <span>检测次数:</span>
                <span>240次</span>
              </div>
            </div>
          </div>

          <div class="process-arrow">
            <i class="fas fa-arrow-right"></i>
          </div>

          <div class="process-step">
            <div class="step-icon">
              <i class="fas fa-screwdriver"></i>
            </div>
            <h4>螺丝拧紧</h4>
            <div class="status online">正常运行</div>
            <div class="step-details">
              <div class="detail-item">
                <span>扭矩设定:</span>
                <span>8.0-8.5 N·m</span>
              </div>
              <div class="detail-item">
                <span>平均扭矩:</span>
                <span>8.25 N·m</span>
              </div>
              <div class="detail-item">
                <span>拧紧次数:</span>
                <span>240×3</span>
              </div>
            </div>
          </div>

          <div class="process-arrow">
            <i class="fas fa-arrow-right"></i>
          </div>

          <div class="process-step">
            <div class="step-icon">
              <i class="fas fa-wind"></i>
            </div>
            <h4>气密性检测</h4>
            <div class="status online">正常运行</div>
            <div class="step-details">
              <div class="detail-item">
                <span>标准值:</span>
                <span>≤0.030 MPa·l/min</span>
              </div>
              <div class="detail-item">
                <span>平均值:</span>
                <span>0.025 MPa·l/min</span>
              </div>
              <div class="detail-item">
                <span>合格率:</span>
                <span>98.3%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 质量分析页面 -->
      <div class="tab-content" id="quality-tab">
        <div class="content-header">
          <h2><i class="fas fa-award"></i> 质量分析</h2>
          <p>深度分析生产质量数据和趋势</p>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
          <div class="filter-group">
            <label>时间范围:</label>
            <select id="qualityTimeRange">
              <option value="today">今天</option>
              <option value="week">本周</option>
              <option value="month">本月</option>
              <option value="quarter">本季度</option>
              <option value="custom">自定义</option>
            </select>
          </div>
          <div class="filter-group">
            <label>检测项目:</label>
            <select id="qualityTestType">
              <option value="all">全部</option>
              <option value="geometry">几何检测</option>
              <option value="airtightness">气密性检测</option>
              <option value="torque">扭矩检测</option>
              <option value="vision">视觉检测</option>
            </select>
          </div>
          <div class="filter-group">
            <label>生产线:</label>
            <select id="qualityProductionLine">
              <option value="all">全部</option>
              <option value="Line-01">Line-01</option>
              <option value="Line-02">Line-02</option>
              <option value="Line-03">Line-03</option>
            </select>
          </div>
          <button class="btn btn-primary" id="applyQualityFilter">
            <i class="fas fa-chart-bar"></i> 应用筛选
          </button>
        </div>

        <div class="quality-metrics">
          <div class="card">
            <div class="card-header">
              <h3>几何检测趋势</h3>
            </div>
            <div class="card-body">
              <canvas id="geometryChart"></canvas>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3>扭矩分布</h3>
            </div>
            <div class="card-body">
              <canvas id="torqueChart"></canvas>
            </div>
          </div>
        </div>

        <div class="quality-analysis">
          <div class="card">
            <div class="card-header">
              <h3><i class="fas fa-exclamation-triangle"></i> 质量异常分析</h3>
            </div>
            <div class="card-body">
              <div class="alert-list" id="qualityAlerts">
                <!-- 动态内容 -->
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 员工管理页面 -->
      <div class="tab-content" id="employee-tab">
        <div class="content-header">
          <h2><i class="fas fa-users"></i> 员工管理</h2>
          <p>管理员工信息、工作记录和绩效分析</p>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
          <div class="filter-group">
            <label>部门:</label>
            <select id="employeeDepartment">
              <option value="all">全部</option>
              <option value="production">生产部</option>
              <option value="quality">质量部</option>
              <option value="maintenance">维护部</option>
              <option value="management">管理部</option>
            </select>
          </div>
          <div class="filter-group">
            <label>班次:</label>
            <select id="employeeShift">
              <option value="all">全部</option>
              <option value="Day-Shift">白班</option>
              <option value="Night-Shift">夜班</option>
            </select>
          </div>
          <div class="filter-group">
            <label>状态:</label>
            <select id="employeeStatus">
              <option value="all">全部</option>
              <option value="working">在岗</option>
              <option value="break">休息</option>
              <option value="training">培训</option>
              <option value="leave">请假</option>
            </select>
          </div>
          <button class="btn btn-primary" id="searchEmployees">
            <i class="fas fa-search"></i> 查询
          </button>
          <button class="btn btn-success" id="addEmployee">
            <i class="fas fa-plus"></i> 新增员工
          </button>
        </div>

        <!-- 员工统计卡片 -->
        <div class="employee-stats">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-user-check"></i>
            </div>
            <div class="stat-content">
              <h3>在岗人数</h3>
              <div class="stat-number" id="workingEmployees">45</div>
              <div class="stat-change">共 52 人</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-trophy"></i>
            </div>
            <div class="stat-content">
              <h3>本月优秀员工</h3>
              <div class="stat-number" id="excellentEmployees">8</div>
              <div class="stat-change positive">+2</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
              <h3>平均工作效率</h3>
              <div class="stat-number" id="avgEfficiency">96.8%</div>
              <div class="stat-change positive">****%</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="stat-content">
              <h3>培训参与率</h3>
              <div class="stat-number" id="trainingRate">88.5%</div>
              <div class="stat-change positive">****%</div>
            </div>
          </div>
        </div>

        <!-- 员工列表 -->
        <div class="card">
          <div class="card-header">
            <h3><i class="fas fa-list"></i> 员工列表</h3>
          </div>
          <div class="card-body">
            <div class="employee-table">
              <table id="employeeTable">
                <thead>
                  <tr>
                    <th>工号</th>
                    <th>姓名</th>
                    <th>部门</th>
                    <th>岗位</th>
                    <th>班次</th>
                    <th>状态</th>
                    <th>今日产量</th>
                    <th>质量分数</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody id="employeeTableBody">
                  <!-- 动态内容 -->
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 员工绩效图表 -->
        <div class="card">
          <div class="card-header">
            <h3><i class="fas fa-chart-line"></i> 员工绩效趋势</h3>
          </div>
          <div class="card-body">
            <canvas id="employeePerformanceChart"></canvas>
          </div>
        </div>
      </div>

      <!-- 设备管理页面 -->
      <div class="tab-content" id="device-tab">
        <div class="content-header">
          <h2><i class="fas fa-microchip"></i> 设备管理</h2>
          <p>监控设备状态、维护记录和性能分析</p>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
          <div class="filter-group">
            <label>设备类型:</label>
            <select id="deviceType">
              <option value="all">全部</option>
              <option value="press">压合设备</option>
              <option value="welding">焊接设备</option>
              <option value="measurement">检测设备</option>
              <option value="assembly">装配设备</option>
              <option value="laser">激光设备</option>
            </select>
          </div>
          <div class="filter-group">
            <label>状态:</label>
            <select id="deviceStatusFilter">
              <option value="all">全部</option>
              <option value="online">在线</option>
              <option value="offline">离线</option>
              <option value="maintenance">维护中</option>
              <option value="error">故障</option>
            </select>
          </div>
          <div class="filter-group">
            <label>生产线:</label>
            <select id="deviceLineFilter">
              <option value="all">全部</option>
              <option value="Line-01">Line-01</option>
              <option value="Line-02">Line-02</option>
              <option value="Line-03">Line-03</option>
            </select>
          </div>
          <button class="btn btn-primary" id="refreshDevices">
            <i class="fas fa-sync"></i> 刷新状态
          </button>
          <button class="btn btn-success" id="addDevice">
            <i class="fas fa-plus"></i> 新增设备
          </button>
        </div>

        <!-- 设备状态概览 -->
        <div class="device-overview">
          <div class="overview-card online">
            <div class="overview-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="overview-content">
              <h3>在线设备</h3>
              <div class="overview-number" id="onlineDeviceCount">24</div>
            </div>
          </div>

          <div class="overview-card warning">
            <div class="overview-icon">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="overview-content">
              <h3>告警设备</h3>
              <div class="overview-number" id="warningDeviceCount">2</div>
            </div>
          </div>

          <div class="overview-card error">
            <div class="overview-icon">
              <i class="fas fa-times-circle"></i>
            </div>
            <div class="overview-content">
              <h3>故障设备</h3>
              <div class="overview-number" id="errorDeviceCount">1</div>
            </div>
          </div>

          <div class="overview-card maintenance">
            <div class="overview-icon">
              <i class="fas fa-tools"></i>
            </div>
            <div class="overview-content">
              <h3>维护设备</h3>
              <div class="overview-number" id="maintenanceDeviceCount">3</div>
            </div>
          </div>
        </div>

        <!-- 设备列表 -->
        <div class="card">
          <div class="card-header">
            <h3><i class="fas fa-list"></i> 设备列表</h3>
          </div>
          <div class="card-body">
            <div class="device-grid" id="deviceGrid">
              <!-- 动态内容 -->
            </div>
          </div>
        </div>

        <!-- 设备性能图表 -->
        <div class="card">
          <div class="card-header">
            <h3><i class="fas fa-chart-area"></i> 设备利用率趋势</h3>
          </div>
          <div class="card-body">
            <canvas id="deviceUtilizationChart"></canvas>
          </div>
        </div>
      </div>

      <!-- 3D拧螺丝指引页面 -->
      <div class="tab-content" id="screw-guide-tab">
        <div class="content-header">
          <h2><i class="fas fa-tools"></i> 3D拧螺丝指引</h2>
          <p>可视化展示螺丝拧紧顺序和位置</p>
        </div>
        
        <div class="screw-guide-container">
          <div id="screw-guide-container" class="guide-view"></div>
          <div class="guide-controls">
            <button class="btn btn-primary" id="prev-screw-btn">
              <i class="fas fa-arrow-left"></i> 上一个
            </button>
            <button class="btn btn-primary" id="next-screw-btn">
              <i class="fas fa-arrow-right"></i> 下一个
            </button>
          </div>
        </div>
      </div>

      <!-- 报表中心页面 -->
      <div class="tab-content" id="report-tab">
        <div class="content-header">
          <h2><i class="fas fa-file-alt"></i> 报表中心</h2>
          <p>生成各类生产报表和数据导出</p>
        </div>

        <!-- 报表类型选择 -->
        <div class="report-types">
          <div class="report-type-card" data-type="production">
            <div class="report-icon">
              <i class="fas fa-chart-bar"></i>
            </div>
            <h3>生产报表</h3>
            <p>产量统计、效率分析、趋势报告</p>
          </div>

          <div class="report-type-card" data-type="quality">
            <div class="report-icon">
              <i class="fas fa-award"></i>
            </div>
            <h3>质量报表</h3>
            <p>合格率统计、缺陷分析、质量趋势</p>
          </div>

          <div class="report-type-card" data-type="employee">
            <div class="report-icon">
              <i class="fas fa-users"></i>
            </div>
            <h3>员工报表</h3>
            <p>绩效统计、考勤记录、培训记录</p>
          </div>

          <div class="report-type-card" data-type="device">
            <div class="report-icon">
              <i class="fas fa-microchip"></i>
            </div>
            <h3>设备报表</h3>
            <p>运行时间、维护记录、故障统计</p>
          </div>
        </div>

        <!-- 报表配置区域 -->
        <div class="card" id="reportConfig" style="display: none;">
          <div class="card-header">
            <h3><i class="fas fa-cog"></i> 报表配置</h3>
          </div>
          <div class="card-body">
            <form id="reportForm">
              <div class="form-row">
                <div class="form-group">
                  <label for="reportTitle">报表标题:</label>
                  <input type="text" id="reportTitle" placeholder="请输入报表标题">
                </div>
                <div class="form-group">
                  <label for="reportTimeRange">时间范围:</label>
                  <select id="reportTimeRange">
                    <option value="today">今天</option>
                    <option value="week">本周</option>
                    <option value="month">本月</option>
                    <option value="quarter">本季度</option>
                    <option value="year">本年</option>
                    <option value="custom">自定义</option>
                  </select>
                </div>
              </div>

              <div class="form-row" id="customReportDateRange" style="display: none;">
                <div class="form-group">
                  <label for="reportStartDate">开始日期:</label>
                  <input type="date" id="reportStartDate">
                </div>
                <div class="form-group">
                  <label for="reportEndDate">结束日期:</label>
                  <input type="date" id="reportEndDate">
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="reportFormat">导出格式:</label>
                  <select id="reportFormat">
                    <option value="pdf">PDF</option>
                    <option value="excel">Excel</option>
                    <option value="csv">CSV</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="reportFilters">筛选条件:</label>
                  <div class="filter-options" id="reportFilters">
                    <!-- 动态内容 -->
                  </div>
                </div>
              </div>

              <div class="form-actions">
                <button type="button" class="btn btn-primary" id="generateReport">
                  <i class="fas fa-file-download"></i> 生成报表
                </button>
                <button type="button" class="btn btn-secondary" id="previewReport">
                  <i class="fas fa-eye"></i> 预览报表
                </button>
                <button type="button" class="btn btn-outline" id="cancelReport">
                  <i class="fas fa-times"></i> 取消
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- 历史报表 -->
        <div class="card">
          <div class="card-header">
            <h3><i class="fas fa-history"></i> 历史报表</h3>
          </div>
          <div class="card-body">
            <div class="report-history">
              <table id="reportHistoryTable">
                <thead>
                  <tr>
                    <th>报表名称</th>
                    <th>类型</th>
                    <th>时间范围</th>
                    <th>生成时间</th>
                    <th>状态</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody id="reportHistoryBody">
                  <!-- 动态内容 -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- 加载指示器 -->
  <div class="loading" id="loading" style="display: none;">
    <div class="loading-spinner"></div>
    <p>正在加载...</p>
  </div>

  <!-- 模态窗口 -->
  <div class="modal" id="helpModal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3>系统帮助</h3>
        <button class="modal-close" onclick="closeModal('helpModal')">×</button>
      </div>
      <div class="modal-body">
        <h4>功能介绍</h4>
        <p>银轮机械生产回溯系统提供全面的生产数据管理和分析功能：</p>
        <ul>
          <li><strong>产品追溯：</strong>通过总成ID快速查询产品完整生产档案</li>
          <li><strong>数据看板：</strong>实时监控生产数据和关键指标</li>
          <li><strong>生产流程：</strong>监控生产线状态和工艺参数</li>
          <li><strong>质量分析：</strong>深度分析质量数据和趋势</li>
          <li><strong>员工管理：</strong>管理员工信息和绩效</li>
          <li><strong>设备管理：</strong>监控设备状态和维护</li>
          <li><strong>报表中心：</strong>生成各类报表和数据导出</li>
        </ul>
      </div>
    </div>
  </div>

  <!-- 员工详情模态窗口 -->
  <div class="modal" id="employeeModal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="employeeModalTitle">员工详情</h3>
        <button class="modal-close" onclick="closeModal('employeeModal')">×</button>
      </div>
      <div class="modal-body" id="employeeModalBody">
        <!-- 动态内容 -->
      </div>
    </div>
  </div>

  <!-- 设备详情模态窗口 -->
  <div class="modal" id="deviceModal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="deviceModalTitle">设备详情</h3>
        <button class="modal-close" onclick="closeModal('deviceModal')">×</button>
      </div>
      <div class="modal-body" id="deviceModalBody">
        <!-- 动态内容 -->
      </div>
    </div>
  </div>

  <!-- 报表预览模态窗口 -->
  <div class="modal" id="reportPreviewModal" style="display: none;">
    <div class="modal-content large">
      <div class="modal-header">
        <h3 id="reportPreviewTitle">报表预览</h3>
        <button class="modal-close" onclick="closeModal('reportPreviewModal')">×</button>
      </div>
      <div class="modal-body" id="reportPreviewBody">
        <!-- 动态内容 -->
      </div>
    </div>
  </div>

  <!-- 技术文档模态窗口 -->
  <div class="modal" id="docsModal" style="display: none;">
    <div class="modal-content fullscreen">
      <div class="modal-header docs-header">
        <div class="docs-header-left">
          <h3><i class="fas fa-book"></i> 生产回溯系统技术文档</h3>
          <div class="docs-tabs">
            <button class="docs-tab active" data-doc="tech">技术实现方案</button>
            <button class="docs-tab" data-doc="production">生产方案</button>
          </div>
        </div>
        <div class="docs-header-right">
          <button class="btn btn-sm" id="toggleTocBtn">
            <i class="fas fa-list"></i> 目录
          </button>
          <button class="btn btn-sm" id="syncDocsBtn" title="从Markdown文件同步内容到JavaScript模块">
            <i class="fas fa-sync-alt"></i> 同步
          </button>
          <button class="btn btn-sm" id="printDocsBtn">
            <i class="fas fa-print"></i> 打印
          </button>
          <button class="modal-close" onclick="closeModal('docsModal')">×</button>
        </div>
      </div>
      <div class="modal-body docs-body">
        <div class="docs-sidebar" id="docsSidebar">
          <div class="docs-toc" id="docsToc">
            <!-- 目录会在这里动态生成 -->
          </div>
        </div>
        <div class="docs-content" id="docsContent">
          <div class="docs-loading">
            <i class="fas fa-spinner fa-spin"></i>
            正在加载文档...
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 水印层 -->
  <div class="watermark" id="watermark">
    <div class="watermark-content">
      襄智汇开发原型专为银轮机械数据回溯系统展示，不做其他作用
    </div>
  </div>

  <style>
    /* 水印样式 */
    .watermark {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 9999;
      overflow: hidden;
    }

    .watermark-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(-45deg);
      font-size: 28px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.08);
      white-space: nowrap;
      user-select: none;
      font-family: 'Microsoft YaHei', sans-serif;
    }

    /* 多重水印效果 */
    .watermark::before {
      content: '襄智汇开发原型专为银轮机械数据回溯系统展示，不做其他作用';
      position: absolute;
      top: 20%;
      left: 20%;
      transform: rotate(-45deg);
      font-size: 24px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.06);
      white-space: nowrap;
      user-select: none;
      font-family: 'Microsoft YaHei', sans-serif;
    }

    .watermark::after {
      content: '襄智汇开发原型专为银轮机械数据回溯系统展示，不做其他作用';
      position: absolute;
      bottom: 20%;
      right: 20%;
      transform: rotate(-45deg);
      font-size: 24px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.06);
      white-space: nowrap;
      user-select: none;
      font-family: 'Microsoft YaHei', sans-serif;
    }

    /* 确保水印不影响页面交互 */
    .watermark * {
      pointer-events: none;
      user-select: none;
    }

    /* 防止水印被覆盖 */
    .watermark {
      z-index: 2147483647 !important;
    }
  </style>

  <!-- 检查Font Awesome是否加载成功 -->
  <script>
    // 检查Font Awesome是否加载成功
    function checkFontAwesome() {
      // 创建一个测试元素
      const testElement = document.createElement('i');
      testElement.className = 'fas fa-home';
      testElement.style.position = 'absolute';
      testElement.style.left = '-9999px';
      document.body.appendChild(testElement);

      // 检查字体是否加载
      const computedStyle = window.getComputedStyle(testElement, ':before');
      const fontFamily = computedStyle.getPropertyValue('font-family');

      // 清理测试元素
      document.body.removeChild(testElement);

      // 如果Font Awesome没有加载，启用备用方案
      if (!fontFamily.includes('Font Awesome')) {
        console.warn('Font Awesome 图标字体加载失败，启用备用图标显示方案');

        // 为body添加备用图标类
        document.body.classList.add('icon-fallback');

        // 显示提示信息
        const notification = document.createElement('div');
        notification.style.cssText = `
          position: fixed;
          top: 10px;
          right: 10px;
          background: #fef3c7;
          color: #92400e;
          padding: 10px 15px;
          border-radius: 5px;
          border: 1px solid #fbbf24;
          z-index: 10000;
          font-size: 14px;
          max-width: 300px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        `;
        notification.innerHTML = `
          <div style="display: flex; align-items: center;">
            <span style="margin-right: 8px;">⚠️</span>
            <div>
              <div style="font-weight: bold;">图标字体加载失败</div>
              <div style="font-size: 12px; margin-top: 2px;">正在使用备用显示方案</div>
            </div>
          </div>
        `;
        document.body.appendChild(notification);

        // 8秒后自动隐藏提示
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 8000);

        // 添加点击关闭功能
        notification.addEventListener('click', () => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        });
        notification.style.cursor = 'pointer';
        notification.title = '点击关闭';

        return false; // 返回false表示Font Awesome加载失败
      }

      console.log('Font Awesome 图标字体加载成功');
      return true; // 返回true表示Font Awesome加载成功
    }

    // 页面加载完成后检查
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(checkFontAwesome, 1000);
      });
    } else {
      setTimeout(checkFontAwesome, 1000);
    }
  </script>
</body>

</html>