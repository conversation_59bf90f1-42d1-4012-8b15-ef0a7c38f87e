/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Font Awesome 备用样式 - 当CDN加载失败时显示文字 */
.fas, .far, .fab, .fal, .fad, .fass, .fasr, .fasl, .fast {
  font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 5 Free", "Font Awesome 5 Pro", sans-serif;
  font-weight: 900;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

/* 当图标字体加载失败时的备用显示 */
.fas::before, .far::before, .fab::before {
  content: "●";
  color: var(--primary-color, #3b82f6);
}

/* 特定图标的备用文字 */
.fa-industry::before { content: "🏭"; }
.fa-book::before { content: "📖"; }
.fa-question-circle::before { content: "❓"; }
.fa-chart-line::before { content: "📈"; }
.fa-search::before { content: "🔍"; }
.fa-cogs::before { content: "⚙️"; }
.fa-award::before { content: "🏆"; }
.fa-users::before { content: "👥"; }
.fa-microchip::before { content: "💻"; }
.fa-tools::before { content: "🔧"; }
.fa-file-alt::before { content: "📄"; }
.fa-boxes::before { content: "📦"; }
.fa-check-circle::before { content: "✅"; }
.fa-clock::before { content: "⏰"; }
.fa-cube::before { content: "📦"; }
.fa-puzzle-piece::before { content: "🧩"; }
.fa-fire::before { content: "🔥"; }
.fa-wind::before { content: "💨"; }
.fa-ruler::before { content: "📏"; }
.fa-screwdriver::before { content: "🔩"; }
.fa-layer-group::before { content: "📚"; }
.fa-compress-arrows-alt::before { content: "⬇️"; }
.fa-arrow-right::before { content: "→"; }
.fa-arrow-left::before { content: "←"; }
.fa-sync::before { content: "🔄"; }
.fa-filter::before { content: "🔽"; }
.fa-plus::before { content: "➕"; }
.fa-times::before { content: "❌"; }
.fa-eye::before { content: "👁️"; }
.fa-download::before { content: "⬇️"; }
.fa-print::before { content: "🖨️"; }
.fa-list::before { content: "📋"; }
.fa-history::before { content: "🕐"; }
.fa-spinner::before { content: "⏳"; }
.fa-exclamation-triangle::before { content: "⚠️"; }
.fa-times-circle::before { content: "❌"; }
.fa-user-check::before { content: "👤"; }
.fa-trophy::before { content: "🏆"; }
.fa-graduation-cap::before { content: "🎓"; }
.fa-chart-bar::before { content: "📊"; }
.fa-chart-area::before { content: "📈"; }
.fa-file-download::before { content: "💾"; }
.fa-sync-alt::before { content: "🔄"; }

/* 根变量定义 */
:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --bg-primary: #f8fafc;
  --bg-secondary: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
}

/* 基础样式 */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  padding: 1rem 0;
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.current-time {
  font-size: 0.875rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
}

/* 按钮样式 */
.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: #475569;
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: #059669;
}

.btn-outline {
  background-color: transparent;
  color: white;
  border: 1px solid white;
}

.btn-outline:hover {
  background-color: white;
  color: var(--primary-color);
}

/* 主要内容区域 */
.main-content {
  display: flex;
  min-height: calc(100vh - 80px);
}

/* 侧边栏样式 */
.sidebar {
  width: 250px;
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  padding: 2rem 0;
}

.nav {
  display: flex;
  flex-direction: column;
}

.nav-item {
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-right: 3px solid transparent;
}

.nav-item:hover {
  background-color: var(--bg-primary);
}

.nav-item.active {
  background-color: var(--bg-primary);
  border-right-color: var(--primary-color);
  color: var(--primary-color);
}

.nav-item i {
  font-size: 1.125rem;
  width: 20px;
}

.nav-item span {
  font-weight: 500;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

.content-header {
  margin-bottom: 2rem;
}

.content-header h2 {
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.content-header p {
  color: var(--text-secondary);
  font-size: 1rem;
}

/* 筛选条件样式 */
.filter-section {
  background-color: var(--bg-secondary);
  padding: 1.5rem;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  margin-bottom: 2rem;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  min-width: 120px;
}

.filter-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.filter-group select,
.filter-group input {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

/* 搜索区域 */
.search-section {
  background-color: var(--bg-secondary);
  padding: 2rem;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  margin-bottom: 2rem;
}

.search-box label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.input-group {
  display: flex;
  gap: 0.5rem;
}

.input-group input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.input-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.card {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  margin-bottom: 2rem;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
}

.card-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-body {
  padding: 1.5rem;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.data-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
}

.data-item:last-child {
  border-bottom: none;
}

.data-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.data-value {
  font-weight: 600;
  text-align: right;
}

.data-value.pass {
  color: var(--success-color);
}

.data-value.fail {
  color: var(--error-color);
}

.data-value.warning {
  color: var(--warning-color);
}

/* 时间轴样式 */
.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
  padding-left: 2rem;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -0.5rem;
  top: 0.5rem;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--primary-color);
  border: 3px solid white;
  box-shadow: 0 0 0 3px var(--border-color);
}

.timeline-time {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.timeline-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.timeline-desc {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* 看板统计卡片 */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: linear-gradient(135deg, var(--bg-secondary), #f8fafc);
  border-radius: var(--radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-md);
  transition: transform 0.2s ease;
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.stat-content h3 {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.stat-change {
  font-size: 0.875rem;
  font-weight: 500;
}

.stat-change.positive {
  color: var(--success-color);
}

.stat-change.negative {
  color: var(--error-color);
}

/* 图表区域 */
.chart-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

/* 图表Canvas样式 */
#productionChart,
#qualityChart,
#geometryChart,
#torqueChart,
#employeePerformanceChart,
#deviceUtilizationChart {
  width: 100% !important;
  height: 300px !important;
  min-height: 300px;
}

/* 生产流程图 */
.process-diagram {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 2rem 0;
}

.process-step {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--bg-secondary), #f8fafc);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  min-width: 200px;
  transition: transform 0.2s ease;
}

.process-step:hover {
  transform: translateY(-2px);
}

.step-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 1rem;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.process-step h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.step-details {
  margin-top: 1rem;
  text-align: left;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
  font-size: 0.875rem;
}

.detail-item span:first-child {
  color: var(--text-secondary);
}

.detail-item span:last-child {
  font-weight: 500;
}

.warning-value {
  color: var(--warning-color);
  font-weight: 600;
}

/* 状态指示器 */
.status {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
  margin: 0.5rem 0;
}

.status.online {
  background-color: #d1fae5;
  color: #059669;
}

.status.warning {
  background-color: #fef3c7;
  color: #d97706;
}

.status.error {
  background-color: #fee2e2;
  color: #dc2626;
}

.status.maintenance {
  background-color: #e0e7ff;
  color: #3730a3;
}

/* 流程箭头 */
.process-arrow {
  color: var(--primary-color);
  font-size: 1.5rem;
  margin: 0 0.5rem;
}

/* 质量分析 */
.quality-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.quality-analysis {
  margin-top: 2rem;
}

.alert-list {
  max-height: 300px;
  overflow-y: auto;
}

.alert-item {
  padding: 1rem;
  margin-bottom: 0.5rem;
  border-left: 4px solid;
  border-radius: var(--radius-md);
  background-color: #f8fafc;
}

.alert-item.warning {
  border-left-color: var(--warning-color);
  background-color: #fffbeb;
}

.alert-item.error {
  border-left-color: var(--error-color);
  background-color: #fef2f2;
}

/* 员工管理样式 */
.employee-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.employee-table {
  overflow-x: auto;
}

.employee-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.employee-table th,
.employee-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.employee-table th {
  background-color: #f8fafc;
  font-weight: 600;
  color: var(--text-secondary);
}

.employee-table tbody tr:hover {
  background-color: #f8fafc;
}

.employee-status {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.employee-status.working {
  background-color: #d1fae5;
  color: #059669;
}

.employee-status.break {
  background-color: #fef3c7;
  color: #d97706;
}

.employee-status.training {
  background-color: #e0e7ff;
  color: #3730a3;
}

.employee-status.leave {
  background-color: #fee2e2;
  color: #dc2626;
}

/* 设备管理样式 */
.device-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.overview-card {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: 1rem;
  border-left: 4px solid;
}

.overview-card.online {
  border-left-color: var(--success-color);
}

.overview-card.warning {
  border-left-color: var(--warning-color);
}

.overview-card.error {
  border-left-color: var(--error-color);
}

.overview-card.maintenance {
  border-left-color: var(--primary-color);
}

.overview-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.overview-card.online .overview-icon {
  background-color: #d1fae5;
  color: var(--success-color);
}

.overview-card.warning .overview-icon {
  background-color: #fef3c7;
  color: var(--warning-color);
}

.overview-card.error .overview-icon {
  background-color: #fee2e2;
  color: var(--error-color);
}

.overview-card.maintenance .overview-icon {
  background-color: #e0e7ff;
  color: var(--primary-color);
}

.overview-content h3 {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.overview-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.device-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.device-card {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  transition: transform 0.2s ease;
}

.device-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.device-name {
  font-weight: 600;
  font-size: 1.125rem;
}

.device-type {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.device-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.device-detail {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
}

.device-detail span:first-child {
  color: var(--text-secondary);
}

/* 报表中心样式 */
.report-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.report-type-card {
  background: linear-gradient(135deg, var(--bg-secondary), #f8fafc);
  border-radius: var(--radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-md);
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.report-type-card:hover {
  transform: translateY(-2px);
  border-color: var(--primary-color);
}

.report-type-card.selected {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
}

.report-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 1rem;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.report-type-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.report-type-card p {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.form-group input,
.form-group select {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: #f8fafc;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
}

.report-history {
  overflow-x: auto;
}

.report-history table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.report-history th,
.report-history td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.report-history th {
  background-color: #f8fafc;
  font-weight: 600;
  color: var(--text-secondary);
}

.report-history tbody tr:hover {
  background-color: #f8fafc;
}

.report-status {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.report-status.completed {
  background-color: #d1fae5;
  color: #059669;
}

.report-status.processing {
  background-color: #fef3c7;
  color: #d97706;
}

.report-status.failed {
  background-color: #fee2e2;
  color: #dc2626;
}

/* 加载指示器 */
.loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading p {
  color: var(--text-secondary);
  font-size: 1rem;
}

/* 模态窗口 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 600px;
  width: 90%;
  max-height: 80%;
  overflow-y: auto;
}

.modal-content.large {
  max-width: 900px;
  width: 95%;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 0.25rem;
}

.modal-close:hover {
  color: var(--text-primary);
}

.modal-body {
  padding: 1.5rem;
}

.modal-body h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.modal-body p {
  margin-bottom: 1rem;
}

.modal-body ul {
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.modal-body li {
  margin-bottom: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    padding: 1rem 0;
  }

  .nav {
    flex-direction: row;
    overflow-x: auto;
    padding: 0 1rem;
  }

  .nav-item {
    flex-shrink: 0;
    padding: 0.75rem 1rem;
    border-right: none;
    border-bottom: 3px solid transparent;
  }

  .nav-item.active {
    border-right: none;
    border-bottom-color: var(--primary-color);
  }

  .content {
    padding: 1rem;
  }

  .data-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .chart-section {
    grid-template-columns: 1fr;
  }

  .process-diagram {
    flex-direction: column;
  }

  .process-arrow {
    transform: rotate(90deg);
    margin: 0.5rem 0;
  }

  .filter-section {
    flex-direction: column;
    align-items: stretch;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }
}

/* 打印样式 */
@media print {
  .header,
  .sidebar,
  .filter-section,
  .btn {
    display: none !important;
  }

  .main-content {
    flex-direction: column;
  }

  .content {
    padding: 0;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-primary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}

/* 3D拧螺丝指引页面样式 */
.screw-guide-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 20px;
}

.guide-view {
  width: 80%;
  height: 500px;
  position: relative;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background-color: #f8fafc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.guide-controls {
  display: flex;
  gap: 16px;
  margin-top: 20px;
}

.guide-controls .btn {
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 文档展示样式 */
.modal-content.fullscreen {
  width: 95vw;
  height: 90vh;
  max-width: 1400px;
  margin: 2.5vh auto;
}

.docs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
}

.docs-header-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.docs-header h3 {
  color: white;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.docs-tabs {
  display: flex;
  gap: 0.5rem;
}

.docs-tab {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.docs-tab:hover {
  background: rgba(255, 255, 255, 0.2);
}

.docs-tab.active {
  background: white;
  color: var(--primary-color);
}

.docs-header-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.docs-header-right .btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
}

.docs-header-right .btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.docs-body {
  display: flex;
  height: calc(90vh - 80px);
  padding: 0;
}

.docs-sidebar {
  width: 280px;
  background: var(--bg-primary);
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
  transition: all 0.3s ease;
}

.docs-sidebar.hidden {
  width: 0;
  border: none;
  overflow: hidden;
}

.docs-toc {
  padding: 1rem;
}

.docs-toc ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.docs-toc li {
  margin: 0.25rem 0;
}

.docs-toc a {
  display: block;
  padding: 0.5rem 0.75rem;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.docs-toc a:hover {
  background: var(--primary-color);
  color: white;
}

.docs-toc a.active {
  background: var(--primary-color);
  color: white;
}

.docs-toc .toc-h1 {
  font-weight: 600;
  color: var(--text-primary);
}

.docs-toc .toc-h2 {
  padding-left: 1rem;
}

.docs-toc .toc-h3 {
  padding-left: 1.5rem;
}

.docs-toc .toc-h4 {
  padding-left: 2rem;
}

.docs-content {
  flex: 1;
  overflow-y: auto;
  background: white;
}

.docs-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  gap: 1rem;
}

.docs-loading i {
  font-size: 2rem;
}

/* Markdown 内容样式 */
.markdown-body {
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-body h1 {
  font-size: 2rem;
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 0.5rem;
  color: var(--primary-color);
}

.markdown-body h2 {
  font-size: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.3rem;
  color: var(--primary-color);
}

.markdown-body h3 {
  font-size: 1.25rem;
  color: var(--text-primary);
}

.markdown-body h4 {
  font-size: 1.1rem;
  color: var(--text-primary);
}

.markdown-body p {
  margin-bottom: 1rem;
}

.markdown-body ul,
.markdown-body ol {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.markdown-body li {
  margin-bottom: 0.25rem;
}

.markdown-body table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
  border: 1px solid var(--border-color);
}

.markdown-body th,
.markdown-body td {
  border: 1px solid var(--border-color);
  padding: 0.75rem;
  text-align: left;
}

.markdown-body th {
  background-color: var(--bg-primary);
  font-weight: 600;
}

.markdown-body tr:nth-child(even) {
  background-color: #f8fafc;
}

.markdown-body code {
  background-color: var(--bg-primary);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.875rem;
}

.markdown-body pre {
  background-color: #f6f8fa;
  border-radius: var(--radius-md);
  padding: 1rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.markdown-body pre code {
  background: none;
  padding: 0;
}

.markdown-body blockquote {
  border-left: 4px solid var(--primary-color);
  padding-left: 1rem;
  margin: 1rem 0;
  color: var(--text-secondary);
  font-style: italic;
}

/* Mermaid 图表样式 */
.mermaid {
  text-align: center;
  margin: 2rem 0;
  background: white;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  padding: 1rem;
}

/* 文档打印样式 */
@media print {
  .docs-header,
  .docs-sidebar {
    display: none !important;
  }
  
  .docs-body {
    display: block;
    height: auto;
  }
  
  .docs-content {
    padding: 0;
  }
  
  .markdown-body {
    padding: 1rem;
  }
  
  .mermaid {
    break-inside: avoid;
  }
}
