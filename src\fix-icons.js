/**
 * 图标修复脚本
 * 用于解决Font Awesome图标不显示的问题
 */

// 图标修复管理器
class IconFixManager {
  constructor() {
    this.isFixed = false;
    this.fallbackEnabled = false;
    this.checkInterval = null;
    this.retryCount = 0;
    this.maxRetries = 3;
  }

  // 初始化图标修复
  init() {
    console.log('🔧 启动图标修复管理器...');
    
    // 立即检查一次
    this.checkAndFix();
    
    // 设置定期检查（每5秒检查一次，最多检查3次）
    this.checkInterval = setInterval(() => {
      if (this.retryCount < this.maxRetries && !this.isFixed) {
        this.retryCount++;
        console.log(`🔄 第${this.retryCount}次重试检查图标状态...`);
        this.checkAndFix();
      } else {
        clearInterval(this.checkInterval);
        if (!this.isFixed) {
          console.log('⚠️ 图标修复尝试完成，使用备用方案');
          this.enableFallback();
        }
      }
    }, 5000);
  }

  // 检查并修复图标
  checkAndFix() {
    const fontAwesomeLoaded = this.checkFontAwesome();
    
    if (fontAwesomeLoaded) {
      this.isFixed = true;
      this.showSuccessMessage();
      clearInterval(this.checkInterval);
    } else {
      this.tryFixMethods();
    }
  }

  // 检查Font Awesome是否加载成功
  checkFontAwesome() {
    try {
      // 方法1：检查字体族
      const testElement = document.createElement('i');
      testElement.className = 'fas fa-home';
      testElement.style.position = 'absolute';
      testElement.style.left = '-9999px';
      document.body.appendChild(testElement);
      
      const computedStyle = window.getComputedStyle(testElement, ':before');
      const fontFamily = computedStyle.getPropertyValue('font-family');
      
      document.body.removeChild(testElement);
      
      if (fontFamily.includes('Font Awesome')) {
        return true;
      }

      // 方法2：检查CSS规则
      const stylesheets = Array.from(document.styleSheets);
      for (let stylesheet of stylesheets) {
        try {
          if (stylesheet.href && stylesheet.href.includes('font-awesome')) {
            const rules = Array.from(stylesheet.cssRules || stylesheet.rules || []);
            if (rules.some(rule => rule.selectorText && rule.selectorText.includes('.fa'))) {
              return true;
            }
          }
        } catch (e) {
          // 跨域样式表可能无法访问，忽略错误
        }
      }

      // 方法3：检查是否有Font Awesome类的元素
      const faElements = document.querySelectorAll('[class*="fa-"]');
      if (faElements.length > 0) {
        const firstElement = faElements[0];
        const beforeContent = window.getComputedStyle(firstElement, ':before').content;
        if (beforeContent && beforeContent !== 'none' && beforeContent !== '""') {
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('检查Font Awesome时出错:', error);
      return false;
    }
  }

  // 尝试各种修复方法
  tryFixMethods() {
    console.log('🔧 尝试修复图标加载问题...');

    // 修复方法1：重新加载CSS
    this.reloadFontAwesomeCSS();

    // 修复方法2：添加备用CDN
    this.addBackupCDN();

    // 修复方法3：检查网络连接
    this.checkNetworkConnection();
  }

  // 重新加载Font Awesome CSS
  reloadFontAwesomeCSS() {
    const existingLinks = document.querySelectorAll('link[href*="font-awesome"]');
    existingLinks.forEach(link => {
      const newLink = document.createElement('link');
      newLink.rel = 'stylesheet';
      newLink.href = link.href + '?t=' + Date.now(); // 添加时间戳避免缓存
      newLink.crossOrigin = 'anonymous';
      
      newLink.onload = () => {
        console.log('✅ Font Awesome CSS重新加载成功');
        setTimeout(() => this.checkAndFix(), 1000);
      };
      
      newLink.onerror = () => {
        console.log('❌ Font Awesome CSS重新加载失败');
      };
      
      document.head.appendChild(newLink);
    });
  }

  // 添加备用CDN
  addBackupCDN() {
    const backupCDNs = [
      'https://use.fontawesome.com/releases/v6.5.1/css/all.css',
      'https://maxcdn.bootstrapcdn.com/font-awesome/6.5.1/css/all.min.css',
      'https://pro.fontawesome.com/releases/v6.5.1/css/all.css'
    ];

    backupCDNs.forEach((url, index) => {
      setTimeout(() => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = url;
        link.crossOrigin = 'anonymous';
        
        link.onload = () => {
          console.log(`✅ 备用CDN ${index + 1} 加载成功`);
          setTimeout(() => this.checkAndFix(), 1000);
        };
        
        link.onerror = () => {
          console.log(`❌ 备用CDN ${index + 1} 加载失败`);
        };
        
        document.head.appendChild(link);
      }, index * 2000); // 每2秒尝试一个备用CDN
    });
  }

  // 检查网络连接
  checkNetworkConnection() {
    if (!navigator.onLine) {
      console.log('❌ 网络连接异常，无法加载外部资源');
      this.showNetworkError();
      this.enableFallback();
      return;
    }

    // 尝试加载一个小的测试资源
    const testImg = new Image();
    testImg.onload = () => {
      console.log('✅ 网络连接正常');
    };
    testImg.onerror = () => {
      console.log('❌ 网络连接可能存在问题');
      this.showNetworkError();
    };
    testImg.src = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/webfonts/fa-solid-900.woff2';
  }

  // 启用备用方案
  enableFallback() {
    if (this.fallbackEnabled) return;
    
    console.log('🎨 启用图标备用显示方案');
    document.body.classList.add('icon-fallback');
    this.fallbackEnabled = true;
    
    this.showFallbackMessage();
  }

  // 显示成功消息
  showSuccessMessage() {
    this.showMessage('✅ 图标加载成功', 'success');
  }

  // 显示网络错误消息
  showNetworkError() {
    this.showMessage('❌ 网络连接异常，无法加载图标字体', 'error');
  }

  // 显示备用方案消息
  showFallbackMessage() {
    this.showMessage('🎨 已启用备用图标显示方案', 'warning');
  }

  // 显示消息
  showMessage(text, type = 'info') {
    const colors = {
      success: { bg: '#d1fae5', color: '#065f46', border: '#10b981' },
      error: { bg: '#fee2e2', color: '#991b1b', border: '#ef4444' },
      warning: { bg: '#fef3c7', color: '#92400e', border: '#fbbf24' },
      info: { bg: '#dbeafe', color: '#1e40af', border: '#3b82f6' }
    };

    const style = colors[type] || colors.info;

    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${style.bg};
      color: ${style.color};
      padding: 12px 16px;
      border-radius: 6px;
      border: 1px solid ${style.border};
      z-index: 10000;
      font-size: 14px;
      max-width: 350px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      cursor: pointer;
      transition: opacity 0.3s ease;
    `;
    
    notification.innerHTML = text;
    notification.title = '点击关闭';
    
    // 添加关闭功能
    notification.addEventListener('click', () => {
      notification.style.opacity = '0';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    });
    
    document.body.appendChild(notification);
    
    // 自动关闭
    setTimeout(() => {
      if (notification.parentNode) {
        notification.style.opacity = '0';
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 300);
      }
    }, type === 'success' ? 3000 : 8000);
  }

  // 手动触发修复
  manualFix() {
    this.retryCount = 0;
    this.isFixed = false;
    this.checkAndFix();
  }

  // 获取状态
  getStatus() {
    return {
      isFixed: this.isFixed,
      fallbackEnabled: this.fallbackEnabled,
      retryCount: this.retryCount
    };
  }
}

// 创建全局实例
window.iconFixManager = new IconFixManager();

// 页面加载完成后自动启动
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => window.iconFixManager.init(), 1000);
  });
} else {
  setTimeout(() => window.iconFixManager.init(), 1000);
}

// 导出供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = IconFixManager;
}
