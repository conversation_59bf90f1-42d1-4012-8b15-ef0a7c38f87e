# 生产回溯系统技术实现方案

## 文档说明
本文档为《汽车零部件生产方案》的技术实现补充文档，详细说明生产回溯系统的技术架构、数据库设计、接口规范等技术细节，供开发团队参考使用。

## 数据库设计

### 数据表结构设计

#### 主表：总成数据表 (Assembly_Master)
| 字段名 | 数据类型 | 说明 | 约束 |
|--------|---------|------|------|
| assembly_id | VARCHAR(50) | 总成激光打标ID | 主键, NOT NULL |
| part1_id | VARCHAR(50) | 零件1 ID | 外键, NOT NULL |
| part2_id | VARCHAR(50) | 零件2 ID | 外键, NOT NULL |
| assembly_time | DATETIME | 组装时间 | NOT NULL |
| assembly_operator | VARCHAR(50) | 组装操作员 | NOT NULL |
| laser_marking_time | DATETIME | 激光打标时间 | NOT NULL |
| airtightness_result | DECIMAL(10,3) | 气密性检测结果 | |
| airtightness_standard | DECIMAL(10,3) | 气密性标准值 | |
| airtightness_status | VARCHAR(20) | 气密性检测状态 | DEFAULT 'PENDING' |
| final_quality_status | VARCHAR(20) | 最终质量状态 | DEFAULT 'PENDING' |
| production_line | VARCHAR(50) | 生产线编号 | NOT NULL |
| shift_id | VARCHAR(20) | 班次 | |
| create_time | DATETIME | 记录创建时间 | DEFAULT CURRENT_TIMESTAMP |
| update_time | DATETIME | 记录更新时间 | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

#### 零件1数据表 (Part1_Details)
| 字段名 | 数据类型 | 说明 | 约束 |
|--------|---------|------|------|
| part1_id | VARCHAR(50) | 零件1 ID | 主键, NOT NULL |
| material_batch | VARCHAR(50) | 原料批次号 | NOT NULL |
| stacking_operator | VARCHAR(50) | 叠放操作员 | |
| stacking_time | DATETIME | 叠放时间 | |
| vision_check_result | VARCHAR(20) | 视觉检测结果 | |
| vision_check_time | DATETIME | 视觉检测时间 | |
| vision_check_score | DECIMAL(5,2) | 视觉检测评分 | |
| pressing_pressure | DECIMAL(10,2) | 压制压力值(MPa) | |
| pressing_time | DATETIME | 压制时间 | |
| pressing_duration | INT | 压制持续时间(秒) | |
| welding_temperature | DECIMAL(10,2) | 焊接温度(°C) | |
| welding_current | DECIMAL(10,2) | 焊接电流(A) | |
| welding_time | DATETIME | 焊接时间 | |
| welding_duration | INT | 焊接持续时间(秒) | |
| airtightness_result | DECIMAL(10,3) | 零件气密性检测结果 | |
| airtightness_standard | DECIMAL(10,3) | 零件气密性标准值 | |
| airtightness_status | VARCHAR(20) | 零件气密性检测状态 | DEFAULT 'PENDING' |
| airtightness_time | DATETIME | 零件气密性检测时间 | |
| height_measurement | DECIMAL(10,3) | 高度检测值(mm) | |
| height_standard | DECIMAL(10,3) | 高度标准值(mm) | |
| height_tolerance | DECIMAL(10,3) | 高度公差(mm) | |
| height_deviation | DECIMAL(10,3) | 高度偏差(mm) | |
| flatness_measurement | DECIMAL(10,3) | 平整性检测值(mm) | |
| flatness_standard | DECIMAL(10,3) | 平整性标准值(mm) | |
| flatness_tolerance | DECIMAL(10,3) | 平整性公差(mm) | |
| flatness_deviation | DECIMAL(10,3) | 平整性偏差(mm) | |
| geometry_check_time | DATETIME | 几何检测时间 | |
| geometry_status | VARCHAR(20) | 几何检测状态 | DEFAULT 'PENDING' |
| laser_marking_time | DATETIME | 激光打标时间 | NOT NULL |
| quality_status | VARCHAR(20) | 零件质量状态 | DEFAULT 'PENDING' |
| create_time | DATETIME | 记录创建时间 | DEFAULT CURRENT_TIMESTAMP |
| update_time | DATETIME | 记录更新时间 | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

#### 零件2数据表 (Part2_Details)
| 字段名 | 数据类型 | 说明 | 约束 |
|--------|---------|------|------|
| part2_id | VARCHAR(50) | 零件2 ID | 主键, NOT NULL |
| material_batch | VARCHAR(50) | 原料批次号 | NOT NULL |
| laser_marking_time | DATETIME | 激光打标时间 | NOT NULL |
| screw1_torque | DECIMAL(10,3) | 螺丝1扭矩值(N·m) | |
| screw1_torque_time | DATETIME | 螺丝1安装时间 | |
| screw1_status | VARCHAR(20) | 螺丝1状态 | DEFAULT 'PENDING' |
| screw1_angle | DECIMAL(10,2) | 螺丝1转角(度) | |
| screw2_torque | DECIMAL(10,3) | 螺丝2扭矩值(N·m) | |
| screw2_torque_time | DATETIME | 螺丝2安装时间 | |
| screw2_status | VARCHAR(20) | 螺丝2状态 | DEFAULT 'PENDING' |
| screw2_angle | DECIMAL(10,2) | 螺丝2转角(度) | |
| screw3_torque | DECIMAL(10,3) | 螺丝3扭矩值(N·m) | |
| screw3_torque_time | DATETIME | 螺丝3安装时间 | |
| screw3_status | VARCHAR(20) | 螺丝3状态 | DEFAULT 'PENDING' |
| screw3_angle | DECIMAL(10,2) | 螺丝3转角(度) | |
| torque_standard_min | DECIMAL(10,3) | 扭矩标准下限(N·m) | NOT NULL |
| torque_standard_max | DECIMAL(10,3) | 扭矩标准上限(N·m) | NOT NULL |
| air_switch_install_time | DATETIME | 空气开关安装时间 | |
| air_switch_status | VARCHAR(20) | 空气开关状态 | DEFAULT 'PENDING' |
| air_switch_test_result | VARCHAR(20) | 空气开关测试结果 | |
| quality_status | VARCHAR(20) | 零件质量状态 | DEFAULT 'PENDING' |
| create_time | DATETIME | 记录创建时间 | DEFAULT CURRENT_TIMESTAMP |
| update_time | DATETIME | 记录更新时间 | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

### 索引设计

```sql
-- 主表索引
CREATE INDEX idx_assembly_time ON Assembly_Master(assembly_time);
CREATE INDEX idx_assembly_line ON Assembly_Master(production_line);
CREATE INDEX idx_assembly_status ON Assembly_Master(final_quality_status);
CREATE INDEX idx_assembly_shift ON Assembly_Master(shift_id);

-- 零件1表索引
CREATE INDEX idx_part1_material ON Part1_Details(material_batch);
CREATE INDEX idx_part1_quality ON Part1_Details(quality_status);
CREATE INDEX idx_part1_geometry ON Part1_Details(geometry_status);
CREATE INDEX idx_part1_time ON Part1_Details(laser_marking_time);

-- 零件2表索引
CREATE INDEX idx_part2_material ON Part2_Details(material_batch);
CREATE INDEX idx_part2_quality ON Part2_Details(quality_status);
CREATE INDEX idx_part2_time ON Part2_Details(laser_marking_time);

-- 外键索引
CREATE INDEX idx_assembly_part1 ON Assembly_Master(part1_id);
CREATE INDEX idx_assembly_part2 ON Assembly_Master(part2_id);
```

### 数据约束和触发器

```sql
-- 添加外键约束
ALTER TABLE Assembly_Master 
ADD CONSTRAINT fk_assembly_part1 
FOREIGN KEY (part1_id) REFERENCES Part1_Details(part1_id);

ALTER TABLE Assembly_Master 
ADD CONSTRAINT fk_assembly_part2 
FOREIGN KEY (part2_id) REFERENCES Part2_Details(part2_id);

-- 检查约束
ALTER TABLE Part1_Details 
ADD CONSTRAINT chk_height_deviation 
CHECK (ABS(height_deviation) <= height_tolerance);

ALTER TABLE Part1_Details 
ADD CONSTRAINT chk_flatness_deviation 
CHECK (ABS(flatness_deviation) <= flatness_tolerance);

ALTER TABLE Part2_Details 
ADD CONSTRAINT chk_torque_range 
CHECK (screw1_torque BETWEEN torque_standard_min AND torque_standard_max);

-- 自动计算偏差的触发器
DELIMITER //
CREATE TRIGGER tr_part1_calculate_deviation 
BEFORE INSERT OR UPDATE ON Part1_Details
FOR EACH ROW
BEGIN
    SET NEW.height_deviation = NEW.height_measurement - NEW.height_standard;
    SET NEW.flatness_deviation = NEW.flatness_measurement - NEW.flatness_standard;
END//
DELIMITER ;
```

## 核心SQL查询语句

### 基础查询

#### 1. 总成基本信息查询
```sql
SELECT 
    assembly_id,
    part1_id,
    part2_id,
    assembly_time,
    assembly_operator,
    airtightness_result,
    airtightness_standard,
    final_quality_status,
    production_line,
    shift_id
FROM Assembly_Master 
WHERE assembly_id = ?;
```

#### 2. 零件1完整数据查询
```sql
SELECT 
    a.assembly_id,
    p1.part1_id,
    p1.material_batch,
    p1.stacking_operator,
    p1.stacking_time,
    p1.vision_check_result,
    p1.vision_check_score,
    p1.pressing_pressure,
    p1.pressing_time,
    p1.welding_temperature,
    p1.welding_current,
    p1.welding_time,
    p1.airtightness_result,
    p1.airtightness_standard,
    p1.airtightness_status,
    p1.airtightness_time,
    p1.height_measurement,
    p1.height_standard,
    p1.height_deviation,
    p1.height_tolerance,
    p1.flatness_measurement,
    p1.flatness_standard,
    p1.flatness_deviation,
    p1.flatness_tolerance,
    p1.geometry_status,
    p1.quality_status
FROM Assembly_Master a
JOIN Part1_Details p1 ON a.part1_id = p1.part1_id
WHERE a.assembly_id = ?;
```

#### 3. 零件2完整数据查询
```sql
SELECT 
    a.assembly_id,
    p2.part2_id,
    p2.material_batch,
    p2.screw1_torque,
    p2.screw1_status,
    p2.screw1_angle,
    p2.screw2_torque,
    p2.screw2_status,
    p2.screw2_angle,
    p2.screw3_torque,
    p2.screw3_status,
    p2.screw3_angle,
    p2.torque_standard_min,
    p2.torque_standard_max,
    p2.air_switch_status,
    p2.air_switch_test_result,
    p2.quality_status
FROM Assembly_Master a
JOIN Part2_Details p2 ON a.part2_id = p2.part2_id
WHERE a.assembly_id = ?;
```

#### 4. 全量数据综合查询
```sql
SELECT 
    a.assembly_id,
    a.assembly_time,
    a.assembly_operator,
    a.airtightness_result,
    a.airtightness_standard,
    a.final_quality_status,
    
    -- 零件1数据
    p1.part1_id,
    p1.material_batch as part1_material_batch,
    p1.height_measurement,
    p1.height_standard,
    p1.height_deviation,
    p1.flatness_measurement,
    p1.flatness_standard,
    p1.flatness_deviation,
    p1.geometry_status,
    p1.quality_status as part1_quality_status,
    
    -- 零件2数据
    p2.part2_id,
    p2.material_batch as part2_material_batch,
    p2.screw1_torque,
    p2.screw2_torque,
    p2.screw3_torque,
    p2.air_switch_status,
    p2.quality_status as part2_quality_status
    
FROM Assembly_Master a
JOIN Part1_Details p1 ON a.part1_id = p1.part1_id
JOIN Part2_Details p2 ON a.part2_id = p2.part2_id
WHERE a.assembly_id = ?;
```

### 统计分析查询

#### 5. 质量统计查询
```sql
-- 按日期统计质量状态
SELECT 
    DATE(assembly_time) as production_date,
    COUNT(*) as total_count,
    SUM(CASE WHEN final_quality_status = 'PASS' THEN 1 ELSE 0 END) as pass_count,
    SUM(CASE WHEN final_quality_status = 'FAIL' THEN 1 ELSE 0 END) as fail_count,
    ROUND(SUM(CASE WHEN final_quality_status = 'PASS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as pass_rate
FROM Assembly_Master 
WHERE assembly_time >= ? AND assembly_time <= ?
GROUP BY DATE(assembly_time)
ORDER BY production_date;
```

#### 6. 缺陷分析查询
```sql
-- 零件1几何缺陷分析
SELECT 
    COUNT(*) as total_count,
    SUM(CASE WHEN ABS(height_deviation) > height_tolerance THEN 1 ELSE 0 END) as height_defects,
    SUM(CASE WHEN ABS(flatness_deviation) > flatness_tolerance THEN 1 ELSE 0 END) as flatness_defects,
    AVG(height_deviation) as avg_height_deviation,
    AVG(flatness_deviation) as avg_flatness_deviation
FROM Part1_Details 
WHERE laser_marking_time >= ? AND laser_marking_time <= ?;
```

#### 7. 扭矩分析查询
```sql
-- 零件2扭矩分析
SELECT 
    AVG(screw1_torque) as avg_screw1_torque,
    AVG(screw2_torque) as avg_screw2_torque,
    AVG(screw3_torque) as avg_screw3_torque,
    STDDEV(screw1_torque) as std_screw1_torque,
    STDDEV(screw2_torque) as std_screw2_torque,
    STDDEV(screw3_torque) as std_screw3_torque,
    COUNT(*) as total_count
FROM Part2_Details 
WHERE laser_marking_time >= ? AND laser_marking_time <= ?
  AND quality_status = 'PASS';
```

### 批次追溯查询

#### 8. 原料批次追溯
```sql
-- 根据原料批次查询所有相关产品
SELECT DISTINCT
    a.assembly_id,
    a.assembly_time,
    a.final_quality_status,
    'Part1' as source_part,
    p1.material_batch
FROM Assembly_Master a
JOIN Part1_Details p1 ON a.part1_id = p1.part1_id
WHERE p1.material_batch = ?

UNION

SELECT DISTINCT
    a.assembly_id,
    a.assembly_time,
    a.final_quality_status,
    'Part2' as source_part,
    p2.material_batch
FROM Assembly_Master a
JOIN Part2_Details p2 ON a.part2_id = p2.part2_id
WHERE p2.material_batch = ?

ORDER BY assembly_time;
```

## 数据采集接口规范

### 设备通信协议

#### 1. Modbus协议配置
```json
{
    "modbus_config": {
        "protocol": "TCP",
        "ip_address": "*************",
        "port": 502,
        "slave_id": 1,
        "registers": {
            "height_measurement": {
                "address": 40001,
                "type": "HOLDING_REGISTER",
                "data_type": "FLOAT32",
                "scale_factor": 0.001
            },
            "flatness_measurement": {
                "address": 40003,
                "type": "HOLDING_REGISTER",
                "data_type": "FLOAT32",
                "scale_factor": 0.001
            },
            "measurement_status": {
                "address": 40005,
                "type": "HOLDING_REGISTER",
                "data_type": "UINT16"
            }
        }
    }
}
```

#### 2. OPC-UA配置
```json
{
    "opcua_config": {
        "endpoint": "opc.tcp://*************:4840",
        "security_policy": "None",
        "security_mode": "None",
        "namespace": "http://example.com/machinery",
        "nodes": {
            "torque_screw1": "ns=2;s=TorqueStation.Screw1.TorqueValue",
            "torque_screw2": "ns=2;s=TorqueStation.Screw2.TorqueValue", 
            "torque_screw3": "ns=2;s=TorqueStation.Screw3.TorqueValue",
            "station_status": "ns=2;s=TorqueStation.Status"
        }
    }
}
```

### 数据传输格式

#### 3. JSON数据格式标准
```json
{
    "message_header": {
        "message_id": "uuid",
        "timestamp": "2024-01-01T12:00:00.000Z",
        "source_device": "device_id",
        "message_type": "measurement_data"
    },
    "payload": {
        "part_id": "P1240101001",
        "measurement_type": "geometry_check",
        "measurements": {
            "height": {
                "value": 25.123,
                "unit": "mm",
                "tolerance": 0.1,
                "status": "PASS"
            },
            "flatness": {
                "value": 0.023,
                "unit": "mm", 
                "tolerance": 0.05,
                "status": "PASS"
            }
        },
        "quality_status": "PASS"
    }
}
```

### API接口定义

#### 4. 数据采集API
```yaml
openapi: 3.0.0
info:
  title: 生产回溯系统API
  version: 1.0.0

paths:
  /api/v1/data/part1:
    post:
      summary: 上传零件1生产数据
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                part_id:
                  type: string
                  description: 零件ID
                stage:
                  type: string
                  enum: [stacking, vision_check, pressing, welding, geometry_check, laser_marking]
                data:
                  type: object
                  description: 阶段相关数据
      responses:
        '200':
          description: 成功
        '400':
          description: 数据格式错误
        '500':
          description: 服务器错误

  /api/v1/data/part2:
    post:
      summary: 上传零件2生产数据
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                part_id:
                  type: string
                stage:
                  type: string
                  enum: [laser_marking, torque_check, air_switch_install]
                data:
                  type: object
      responses:
        '200':
          description: 成功

  /api/v1/data/assembly:
    post:
      summary: 上传总成数据
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                assembly_id:
                  type: string
                part1_id:
                  type: string
                part2_id:
                  type: string
                stage:
                  type: string
                  enum: [assembly, laser_marking, airtightness_test]
                data:
                  type: object
      responses:
        '200':
          description: 成功

  /api/v1/trace/{assembly_id}:
    get:
      summary: 查询总成追溯数据
      parameters:
        - name: assembly_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 返回完整追溯数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  assembly_info:
                    type: object
                  part1_info:
                    type: object
                  part2_info:
                    type: object
```

## 系统架构

### 微服务架构设计

```mermaid
graph TB
    subgraph "设备层"
        A1[视觉检测设备]
        A2[几何检测设备]
        A3[力矩检测设备]
        A4[激光打标设备]
        A5[气密性检测设备]
    end
    
    subgraph "数据采集层"
        B1[Modbus网关]
        B2[OPC-UA客户端]
        B3[TCP/IP接口]
    end
    
    subgraph "应用服务层"
        C1[数据采集服务]
        C2[数据处理服务]
        C3[追溯查询服务]
        C4[报表服务]
    end
    
    subgraph "数据存储层"
        D1[主数据库<br/>MySQL]
        D2[时序数据库<br/>InfluxDB]
        D3[缓存层<br/>Redis]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B2
    A4 --> B3
    A5 --> B3
    
    B1 --> C1
    B2 --> C1
    B3 --> C1
    
    C1 --> C2
    C2 --> D1
    C2 --> D2
    C3 --> D1
    C3 --> D3
    C4 --> D1
    C4 --> D2
```

### 部署配置

#### Docker Compose配置
```yaml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: production_trace
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  influxdb:
    image: influxdb:2.7
    environment:
      INFLUXDB_DB: production_metrics
      INFLUXDB_ADMIN_USER: admin
      INFLUXDB_ADMIN_PASSWORD: admin_password
    ports:
      - "8086:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2

  data_collector:
    build: ./services/data-collector
    environment:
      DB_HOST: mysql
      DB_USER: root
      DB_PASSWORD: root_password
      REDIS_HOST: redis
      INFLUX_HOST: influxdb
    depends_on:
      - mysql
      - redis
      - influxdb

  trace_service:
    build: ./services/trace-service
    environment:
      DB_HOST: mysql
      REDIS_HOST: redis
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis

volumes:
  mysql_data:
  redis_data:
  influxdb_data:
```

## 性能优化

### 数据库优化策略

1. **分表策略**
```sql
-- 按月分表
CREATE TABLE Assembly_Master_202401 LIKE Assembly_Master;
CREATE TABLE Assembly_Master_202402 LIKE Assembly_Master;
-- 使用存储过程自动路由到对应月份的表
```

2. **读写分离**
```yaml
database:
  master:
    host: db-master
    port: 3306
  slaves:
    - host: db-slave1
      port: 3306
    - host: db-slave2
      port: 3306
```

3. **查询缓存**
```python
# Redis缓存策略
def get_trace_data(assembly_id):
    cache_key = f"trace:{assembly_id}"
    cached_data = redis.get(cache_key)
    
    if cached_data:
        return json.loads(cached_data)
    
    # 从数据库查询
    data = query_from_database(assembly_id)
    
    # 缓存1小时
    redis.setex(cache_key, 3600, json.dumps(data))
    
    return data
```

## 监控和运维

### 监控指标

1. **系统指标**
   - 数据采集成功率
   - API响应时间
   - 数据库连接池使用率
   - 缓存命中率

2. **业务指标**
   - 每日生产数量
   - 质量合格率
   - 数据完整性
   - 追溯查询频率

### 日志规范

```json
{
    "timestamp": "2024-01-01T12:00:00.000Z",
    "level": "INFO",
    "service": "data-collector",
    "trace_id": "uuid",
    "message": "Data collected successfully",
    "context": {
        "device_id": "DEV001",
        "part_id": "P1240101001",
        "data_type": "geometry_measurement"
    }
}
```

## 安全配置

### 数据库安全
```sql
-- 创建专用用户
CREATE USER 'trace_user'@'%' IDENTIFIED BY 'secure_password';
GRANT SELECT, INSERT, UPDATE ON production_trace.* TO 'trace_user'@'%';

-- 敏感数据加密
ALTER TABLE Part1_Details ADD COLUMN operator_encrypted VARBINARY(255);
```

### API安全
```yaml
security:
  jwt:
    secret: ${JWT_SECRET}
    expiration: 86400
  rate_limit:
    requests_per_minute: 100
  cors:
    allowed_origins: 
      - http://localhost:3000
      - https://production-dashboard.company.com
```

这个技术文档涵盖了生产回溯系统的所有技术实现细节，为开发团队提供了完整的技术指导。 