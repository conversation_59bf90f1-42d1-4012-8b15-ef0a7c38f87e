/* 
 * Font Awesome 备用图标样式
 * 当CDN加载失败时使用本地备用方案
 */

/* 基础图标样式 */
.fas, .far, .fab, .fal, .fad, .fass, .fasr, .fasl, .fast {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  font-weight: 900;
}

/* 当Font Awesome字体不可用时的备用显示 */
.icon-fallback .fas::before,
.icon-fallback .far::before,
.icon-fallback .fab::before {
  font-family: "Segoe UI Emoji", "Apple Color Emoji", "Noto Color Emoji", sans-serif;
}

/* 具体图标的备用内容 */
.icon-fallback .fa-industry::before { content: "🏭"; }
.icon-fallback .fa-book::before { content: "📖"; }
.icon-fallback .fa-question-circle::before { content: "❓"; }
.icon-fallback .fa-chart-line::before { content: "📈"; }
.icon-fallback .fa-search::before { content: "🔍"; }
.icon-fallback .fa-cogs::before { content: "⚙️"; }
.icon-fallback .fa-award::before { content: "🏆"; }
.icon-fallback .fa-users::before { content: "👥"; }
.icon-fallback .fa-microchip::before { content: "💻"; }
.icon-fallback .fa-tools::before { content: "🔧"; }
.icon-fallback .fa-file-alt::before { content: "📄"; }
.icon-fallback .fa-boxes::before { content: "📦"; }
.icon-fallback .fa-check-circle::before { content: "✅"; }
.icon-fallback .fa-clock::before { content: "⏰"; }
.icon-fallback .fa-cube::before { content: "📦"; }
.icon-fallback .fa-puzzle-piece::before { content: "🧩"; }
.icon-fallback .fa-fire::before { content: "🔥"; }
.icon-fallback .fa-wind::before { content: "💨"; }
.icon-fallback .fa-ruler::before { content: "📏"; }
.icon-fallback .fa-screwdriver::before { content: "🔩"; }
.icon-fallback .fa-layer-group::before { content: "📚"; }
.icon-fallback .fa-compress-arrows-alt::before { content: "⬇️"; }
.icon-fallback .fa-arrow-right::before { content: "→"; }
.icon-fallback .fa-arrow-left::before { content: "←"; }
.icon-fallback .fa-sync::before { content: "🔄"; }
.icon-fallback .fa-filter::before { content: "🔽"; }
.icon-fallback .fa-plus::before { content: "➕"; }
.icon-fallback .fa-times::before { content: "❌"; }
.icon-fallback .fa-eye::before { content: "👁️"; }
.icon-fallback .fa-download::before { content: "⬇️"; }
.icon-fallback .fa-print::before { content: "🖨️"; }
.icon-fallback .fa-list::before { content: "📋"; }
.icon-fallback .fa-history::before { content: "🕐"; }
.icon-fallback .fa-spinner::before { content: "⏳"; }
.icon-fallback .fa-exclamation-triangle::before { content: "⚠️"; }
.icon-fallback .fa-times-circle::before { content: "❌"; }
.icon-fallback .fa-user-check::before { content: "👤"; }
.icon-fallback .fa-trophy::before { content: "🏆"; }
.icon-fallback .fa-graduation-cap::before { content: "🎓"; }
.icon-fallback .fa-chart-bar::before { content: "📊"; }
.icon-fallback .fa-chart-area::before { content: "📈"; }
.icon-fallback .fa-file-download::before { content: "💾"; }
.icon-fallback .fa-sync-alt::before { content: "🔄"; }
.icon-fallback .fa-home::before { content: "🏠"; }
.icon-fallback .fa-bug::before { content: "🐛"; }

/* 旋转动画 */
.icon-fallback .fa-spin {
  animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 简化版图标 - 使用CSS绘制 */
.icon-simple {
  display: inline-block;
  width: 1em;
  height: 1em;
  position: relative;
  vertical-align: middle;
}

/* 简单的几何图标 */
.icon-simple.icon-circle::before {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: currentColor;
}

.icon-simple.icon-square::before {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  background: currentColor;
}

.icon-simple.icon-triangle::before {
  content: "";
  display: block;
  width: 0;
  height: 0;
  border-left: 0.5em solid transparent;
  border-right: 0.5em solid transparent;
  border-bottom: 0.866em solid currentColor;
}

.icon-simple.icon-diamond::before {
  content: "";
  display: block;
  width: 0.707em;
  height: 0.707em;
  background: currentColor;
  transform: rotate(45deg);
  margin: 0.146em auto;
}

/* 文字图标 */
.icon-text {
  display: inline-block;
  font-size: 0.8em;
  font-weight: bold;
  padding: 0.1em 0.3em;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 0.2em;
  color: #3b82f6;
  margin-right: 0.3em;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 0.8em;
  height: 0.8em;
  border-radius: 50%;
  margin-right: 0.5em;
}

.status-indicator.online {
  background: #10b981;
}

.status-indicator.warning {
  background: #f59e0b;
}

.status-indicator.error {
  background: #ef4444;
}

.status-indicator.offline {
  background: #6b7280;
}

/* 响应式图标大小 */
.icon-xs { font-size: 0.75em; }
.icon-sm { font-size: 0.875em; }
.icon-lg { font-size: 1.25em; }
.icon-xl { font-size: 1.5em; }
.icon-2x { font-size: 2em; }
.icon-3x { font-size: 3em; }

/* 图标颜色变体 */
.icon-primary { color: #3b82f6; }
.icon-success { color: #10b981; }
.icon-warning { color: #f59e0b; }
.icon-error { color: #ef4444; }
.icon-muted { color: #6b7280; }

/* 图标间距 */
.icon-mr { margin-right: 0.5em; }
.icon-ml { margin-left: 0.5em; }
.icon-mx { margin-left: 0.25em; margin-right: 0.25em; }

/* 图标按钮样式 */
.icon-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5em;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 0.25em;
  transition: background-color 0.2s;
}

.icon-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}

.icon-btn:active {
  background: rgba(0, 0, 0, 0.1);
}

/* 图标加载状态 */
.icon-loading {
  opacity: 0.6;
  pointer-events: none;
}

.icon-loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 1em;
  height: 1em;
  margin: -0.5em 0 0 -0.5em;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: fa-spin 1s infinite linear;
}

/* 图标组合 */
.icon-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}

.icon-stack .icon-stack-1x,
.icon-stack .icon-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}

.icon-stack .icon-stack-1x {
  line-height: inherit;
}

.icon-stack .icon-stack-2x {
  font-size: 2em;
}

/* 图标列表 */
.icon-list {
  list-style: none;
  padding: 0;
}

.icon-list li {
  position: relative;
  padding-left: 2em;
  margin-bottom: 0.5em;
}

.icon-list li::before {
  position: absolute;
  left: 0;
  top: 0;
  width: 1.5em;
  text-align: center;
}

/* 图标网格 */
.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.icon-grid-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background: #f9fafb;
}

.icon-grid-item .icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
  color: #3b82f6;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .icon-text {
    background: rgba(59, 130, 246, 0.2);
    color: #60a5fa;
  }
  
  .icon-btn:hover {
    background: rgba(255, 255, 255, 0.05);
  }
  
  .icon-btn:active {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .icon-grid-item {
    border-color: #374151;
    background: #1f2937;
  }
}
